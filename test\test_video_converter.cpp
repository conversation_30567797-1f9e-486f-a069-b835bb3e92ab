#include "video_converter.h"
#include "rga_accelerator.h"
#include "mpp_decoder.h"
#include "gstreamer_encoder.h"
#include "config_loader.h"
#include "common.h"
#include <iostream>
#include <chrono>
#include <thread>
#include <vector>
#include <random>
#include <fstream>
#include <memory>
#include <iomanip>
#include <atomic>
#include <cstdio>

// 测试结果统计
struct TestResults {
    uint32_t total_tests = 0;
    uint32_t passed_tests = 0;
    uint32_t failed_tests = 0;
    std::vector<std::string> failed_test_names;

    void add_result(const std::string& test_name, bool passed) {
        total_tests++;
        if (passed) {
            passed_tests++;
            std::cout << "✓ " << test_name << " PASSED" << std::endl;
        } else {
            failed_tests++;
            failed_test_names.push_back(test_name);
            std::cout << "✗ " << test_name << " FAILED" << std::endl;
        }
    }

    void print_summary() {
        std::cout << "\n=== Test Summary ===" << std::endl;
        std::cout << "Total tests: " << total_tests << std::endl;
        std::cout << "Passed: " << passed_tests << std::endl;
        std::cout << "Failed: " << failed_tests << std::endl;

        if (failed_tests > 0) {
            std::cout << "\nFailed tests:" << std::endl;
            for (const auto& name : failed_test_names) {
                std::cout << "  - " << name << std::endl;
            }
        }

        float success_rate = (float)passed_tests / total_tests * 100;
        std::cout << "Success rate: " << std::fixed << std::setprecision(1) << success_rate << "%" << std::endl;
    }
};

// 全局测试结果
TestResults g_test_results;

// 辅助函数：创建测试帧
Frame create_test_frame(int width, int height, int32_t format, uint64_t frame_id = 1) {
    Frame frame;
    frame.width = width;
    frame.height = height;
    frame.format = format;
    frame.frame_id = frame_id;
    frame.timestamp = std::chrono::duration_cast<std::chrono::milliseconds>(
        std::chrono::steady_clock::now().time_since_epoch()).count();
    frame.source_type = 0;
    frame.is_keyframe = true;
    frame.valid = true;

    // 根据格式计算数据大小
    size_t data_size = 0;
    switch (format) {
        case V4L2_PIX_FMT_YUYV:
        case V4L2_PIX_FMT_UYVY:
            data_size = width * height * 2;
            break;
        case V4L2_PIX_FMT_RGB24:
        case V4L2_PIX_FMT_BGR24:
            data_size = width * height * 3;
            break;
        case V4L2_PIX_FMT_NV12:
        case V4L2_PIX_FMT_NV21:
            data_size = width * height * 3 / 2;
            break;
        case V4L2_PIX_FMT_RGBA32:
        case V4L2_PIX_FMT_BGRA32:
            data_size = width * height * 4;
            break;
        case V4L2_PIX_FMT_MJPEG:
            data_size = width * height / 4;  // 估算MJPEG大小
            break;
        default:
            data_size = width * height * 3;  // 默认RGB24
            break;
    }

    frame.data.resize(data_size);

    // 填充测试数据（渐变图案）
    std::random_device rd;
    std::mt19937 gen(rd());
    std::uniform_int_distribution<> dis(0, 255);

    for (size_t i = 0; i < data_size; i++) {
        frame.data[i] = dis(gen);
    }

    return frame;
}

// 辅助函数：验证帧数据
bool validate_frame(const Frame& frame, int expected_width, int expected_height, int32_t expected_format) {
    if (frame.width != expected_width) {
        std::cout << "    Width mismatch: expected " << expected_width << ", got " << frame.width << std::endl;
        return false;
    }
    if (frame.height != expected_height) {
        std::cout << "    Height mismatch: expected " << expected_height << ", got " << frame.height << std::endl;
        return false;
    }
    if (frame.format != expected_format) {
        std::cout << "    Format mismatch: expected 0x" << std::hex << expected_format
                  << ", got 0x" << frame.format << std::dec << std::endl;
        return false;
    }
    if (frame.data.empty()) {
        std::cout << "    Frame data is empty" << std::endl;
        return false;
    }
    if (!frame.valid) {
        std::cout << "    Frame is marked as invalid" << std::endl;
        return false;
    }
    return true;
}

// 简单的测试函数
void test_video_converter_comprehensive() {
    std::cout << "\n=== Comprehensive VideoConverter Tests ===" << std::endl;

    // 测试1: 基本初始化和配置
    {
        std::cout << "\nTest 1: Basic initialization and configuration" << std::endl;
        try {
            VideoConverter converter;
            VideoConverterConfig config;
            config.enable_hardware_acceleration = true;
            config.enable_ai = true;
            config.enable_cloud_streaming = true;
            config.ai_width = 640;
            config.ai_height = 640;
            config.cloud_bitrate = 2000000;

            converter.set_config(config);

            bool init_success = converter.init("Test_Input", "Test_AI_Output", "Test_Cloud_Output");
            g_test_results.add_result("VideoConverter Basic Initialization", init_success);

            if (init_success) {
                std::cout << "    ✓ VideoConverter initialized successfully" << std::endl;
            }
        } catch (const std::exception& e) {
            std::cout << "    ✗ Exception during initialization: " << e.what() << std::endl;
            g_test_results.add_result("VideoConverter Basic Initialization", false);
        }
    }

    // 测试2: 不同配置组合
    {
        std::cout << "\nTest 2: Different configuration combinations" << std::endl;

        struct ConfigTest {
            std::string name;
            bool enable_ai;
            bool enable_cloud;
            bool should_succeed;
        };

        std::vector<ConfigTest> config_tests = {
            {"Both enabled", true, true, true},
            {"AI only", true, false, true},
            {"Cloud only", false, true, true},
            {"Both disabled", false, false, true}  // 应该成功初始化，但会丢弃所有帧
        };

        uint32_t passed = 0;
        for (auto& test : config_tests) {
            try {
                VideoConverter converter;
                VideoConverterConfig config;
                config.enable_ai = test.enable_ai;
                config.enable_cloud_streaming = test.enable_cloud;
                config.ai_width = 640;
                config.ai_height = 640;
                config.cloud_bitrate = 2000000;

                converter.set_config(config);
                bool init_success = converter.init("Test_Input_" + test.name,
                                                 "Test_AI_" + test.name,
                                                 "Test_Cloud_" + test.name);

                if (init_success == test.should_succeed) {
                    std::cout << "    ✓ " << test.name << " configuration test passed" << std::endl;
                    passed++;
                } else {
                    std::cout << "    ✗ " << test.name << " configuration test failed" << std::endl;
                }
            } catch (const std::exception& e) {
                std::cout << "    ✗ " << test.name << " threw exception: " << e.what() << std::endl;
            }
        }

        g_test_results.add_result("VideoConverter Configuration Combinations", passed == config_tests.size());
    }

    // 测试3: 启动和停止测试
    {
        std::cout << "\nTest 3: Start and stop operations" << std::endl;
        try {
            VideoConverter converter;
            VideoConverterConfig config;
            config.enable_ai = true;
            config.enable_cloud_streaming = false;  // 简化测试
            config.ai_width = 640;
            config.ai_height = 640;

            converter.set_config(config);

            if (converter.init("Test_StartStop", "Test_AI_StartStop", "Test_Cloud_StartStop")) {
                // 测试启动
                converter.start();
                std::this_thread::sleep_for(std::chrono::milliseconds(100));

                // 测试停止
                converter.stop();
                std::this_thread::sleep_for(std::chrono::milliseconds(100));

                // 测试重新启动
                converter.start();
                std::this_thread::sleep_for(std::chrono::milliseconds(100));
                converter.stop();

                g_test_results.add_result("VideoConverter Start/Stop", true);
                std::cout << "    ✓ Start/stop operations successful" << std::endl;
            } else {
                g_test_results.add_result("VideoConverter Start/Stop", false);
            }
        } catch (const std::exception& e) {
            std::cout << "    ✗ Start/stop test exception: " << e.what() << std::endl;
            g_test_results.add_result("VideoConverter Start/Stop", false);
        }
    }

    // 测试4: 统计信息测试
    {
        std::cout << "\nTest 4: Statistics functionality" << std::endl;
        try {
            VideoConverter converter;
            VideoConverterConfig config;
            config.enable_ai = true;
            config.enable_cloud_streaming = true;
            config.ai_width = 640;
            config.ai_height = 640;
            config.cloud_bitrate = 2000000;

            converter.set_config(config);

            if (converter.init("Test_Stats", "Test_AI_Stats", "Test_Cloud_Stats")) {
                converter.start();
                std::this_thread::sleep_for(std::chrono::milliseconds(500));

                VideoConverter::Stats stats;
                converter.get_stats(stats);

                // 验证统计信息结构
                bool stats_valid = (stats.frames_processed > 0) &&
                                 (stats.ai_frames_sent > 0) &&
                                 (stats.cloud_frames_sent > 0) &&
                                 (stats.cpu_usage > 0.0f);

                converter.stop();

                g_test_results.add_result("VideoConverter Statistics", stats_valid);

                if (stats_valid) {
                    std::cout << "    ✓ Statistics retrieved successfully" << std::endl;
                    std::cout << "      Frames processed: " << stats.frames_processed << std::endl;
                    std::cout << "      Frames dropped: " << stats.frames_dropped << std::endl;
                    std::cout << "      AI frames sent: " << stats.ai_frames_sent << std::endl;
                    std::cout << "      Cloud frames sent: " << stats.cloud_frames_sent << std::endl;
                    std::cout << "      CPU usage: " << stats.cpu_usage << "%" << std::endl;
                }
            } else {
                g_test_results.add_result("VideoConverter Statistics", false);
            }
        } catch (const std::exception& e) {
            std::cout << "    ✗ Statistics test exception: " << e.what() << std::endl;
            g_test_results.add_result("VideoConverter Statistics", false);
        }
    }

    // 测试5: 信号处理测试
    {
        std::cout << "\nTest 5: Signal handling" << std::endl;
        try {
            VideoConverter converter;
            VideoConverterConfig config;
            config.enable_ai = true;
            config.enable_cloud_streaming = false;
            config.ai_width = 640;
            config.ai_height = 640;

            converter.set_config(config);

            if (converter.init("Test_Signal", "Test_AI_Signal", "Test_Cloud_Signal")) {
                converter.start();
                std::this_thread::sleep_for(std::chrono::milliseconds(100));

                // 测试信号处理（SIGUSR1）
                converter.handle_signal(SIGUSR1);
                std::this_thread::sleep_for(std::chrono::milliseconds(100));

                converter.stop();

                g_test_results.add_result("VideoConverter Signal Handling", true);
                std::cout << "    ✓ Signal handling test completed" << std::endl;
            } else {
                g_test_results.add_result("VideoConverter Signal Handling", false);
            }
        } catch (const std::exception& e) {
            std::cout << "    ✗ Signal handling test exception: " << e.what() << std::endl;
            g_test_results.add_result("VideoConverter Signal Handling", false);
        }
    }

    // 测试6: 错误处理测试
    {
        std::cout << "\nTest 6: VideoConverter error handling" << std::endl;
        bool all_error_tests_passed = true;

        // 子测试1: 测试无效配置
        {
            VideoConverter converter;
            VideoConverterConfig config;
            config.enable_ai = true;
            config.enable_cloud_streaming = true;
            config.ai_width = 0;  // 无效尺寸
            config.ai_height = 0;  // 无效尺寸
            config.cloud_bitrate = -1000000;  // 无效比特率

            converter.set_config(config);

            bool invalid_config_result = converter.init("Test_Invalid", "Test_AI_Invalid", "Test_Cloud_Invalid");
            if (!invalid_config_result) {
                std::cout << "    ✓ Invalid configuration correctly rejected" << std::endl;
            } else {
                std::cout << "    ⚠ Invalid configuration unexpectedly accepted" << std::endl;
                // 不标记为失败，因为某些实现可能有默认值处理
                converter.stop();
            }
        }

        // 子测试2: 测试重复初始化
        {
            VideoConverter converter;
            VideoConverterConfig config;
            config.enable_ai = true;
            config.enable_cloud_streaming = false;
            config.ai_width = 640;
            config.ai_height = 640;

            converter.set_config(config);

            bool first_init = converter.init("Test_Repeat1", "Test_AI_Repeat1", "Test_Cloud_Repeat1");
            bool second_init = converter.init("Test_Repeat2", "Test_AI_Repeat2", "Test_Cloud_Repeat2");

            if (first_init) {
                std::cout << "    ✓ First initialization succeeded" << std::endl;
                if (second_init) {
                    std::cout << "    ✓ Second initialization succeeded (re-initialization allowed)" << std::endl;
                } else {
                    std::cout << "    ⚠ Second initialization failed (re-initialization not allowed)" << std::endl;
                    // 不标记为失败，因为某些实现可能不允许重复初始化
                }
                converter.stop();
            } else {
                std::cout << "    ✗ First initialization failed" << std::endl;
                all_error_tests_passed = false;
            }
        }

        // 子测试3: 测试未初始化操作
        {
            VideoConverter converter;

            // 测试未初始化就启动
            try {
                converter.start();
                std::cout << "    ⚠ Start without initialization did not throw exception" << std::endl;
                converter.stop();
            } catch (const std::exception& e) {
                std::cout << "    ✓ Start without initialization correctly threw exception: " << e.what() << std::endl;
            }

            // 测试未初始化就获取统计信息
            VideoConverter::Stats stats;
            try {
                converter.get_stats(stats);
                std::cout << "    ⚠ Get stats without initialization did not throw exception" << std::endl;
            } catch (const std::exception& e) {
                std::cout << "    ✓ Get stats without initialization correctly threw exception: " << e.what() << std::endl;
            }
        }

        // 子测试4: 测试空主题名称
        {
            VideoConverter converter;
            VideoConverterConfig config;
            config.enable_ai = true;
            config.enable_cloud_streaming = false;
            config.ai_width = 640;
            config.ai_height = 640;

            converter.set_config(config);

            bool empty_topic_result = converter.init("", "", "");  // 空主题名称
            if (!empty_topic_result) {
                std::cout << "    ✓ Empty topic names correctly rejected" << std::endl;
            } else {
                std::cout << "    ⚠ Empty topic names unexpectedly accepted" << std::endl;
                // 不标记为失败，因为某些实现可能有默认主题名称
                converter.stop();
            }
        }

        // 子测试5: 测试多次启动/停止
        {
            VideoConverter converter;
            VideoConverterConfig config;
            config.enable_ai = true;
            config.enable_cloud_streaming = false;
            config.ai_width = 640;
            config.ai_height = 640;

            converter.set_config(config);

            if (converter.init("Test_MultiStart", "Test_AI_MultiStart", "Test_Cloud_MultiStart")) {
                try {
                    // 多次启动
                    converter.start();
                    converter.start();  // 重复启动

                    std::this_thread::sleep_for(std::chrono::milliseconds(50));

                    // 多次停止
                    converter.stop();
                    converter.stop();  // 重复停止

                    std::cout << "    ✓ Multiple start/stop operations handled gracefully" << std::endl;
                } catch (const std::exception& e) {
                    std::cout << "    ✗ Multiple start/stop operations threw exception: " << e.what() << std::endl;
                    all_error_tests_passed = false;
                }
            } else {
                std::cout << "    ✗ Failed to initialize converter for multi-start test" << std::endl;
                all_error_tests_passed = false;
            }
        }

        g_test_results.add_result("VideoConverter Error Handling", all_error_tests_passed);

        if (all_error_tests_passed) {
            std::cout << "    ✓ All VideoConverter error handling tests passed" << std::endl;
        } else {
            std::cout << "    ✗ Some VideoConverter error handling tests failed" << std::endl;
        }
    }
}

void test_rga_accelerator_comprehensive() {
    std::cout << "\n=== Comprehensive RGA Accelerator Tests ===" << std::endl;

    RGAAccelerator rga;
    bool init_success = rga.init();
    g_test_results.add_result("RGA Initialization", init_success);

    if (!init_success) {
        std::cout << "RGA initialization failed, skipping RGA tests" << std::endl;
        return;
    }

    // 测试1: 基本格式转换
    {
        std::cout << "\nTest 1: Basic format conversion (YUYV -> RGB24)" << std::endl;
        Frame src = create_test_frame(1280, 720, V4L2_PIX_FMT_YUYV);
        Frame dst;

        bool success = rga.convert_and_scale(src, dst, 640, 640, V4L2_PIX_FMT_RGB24);
        bool valid = success && validate_frame(dst, 640, 640, V4L2_PIX_FMT_RGB24);
        g_test_results.add_result("RGA Basic Format Conversion", valid);
    }

    // 测试2: 多种格式转换
    {
        std::cout << "\nTest 2: Multiple format conversions" << std::endl;
        std::vector<std::pair<int32_t, std::string>> formats = {
            {V4L2_PIX_FMT_YUYV, "YUYV"},
            {V4L2_PIX_FMT_UYVY, "UYVY"},
            {V4L2_PIX_FMT_RGB24, "RGB24"},
            {V4L2_PIX_FMT_NV12, "NV12"}
        };

        uint32_t passed = 0;
        for (auto& format_pair : formats) {
            Frame src = create_test_frame(640, 480, format_pair.first);
            Frame dst;

            if (rga.format_convert(src, dst, V4L2_PIX_FMT_RGB24)) {
                if (validate_frame(dst, 640, 480, V4L2_PIX_FMT_RGB24)) {
                    std::cout << "    ✓ " << format_pair.second << " -> RGB24 conversion successful" << std::endl;
                    passed++;
                } else {
                    std::cout << "    ✗ " << format_pair.second << " -> RGB24 validation failed" << std::endl;
                }
            } else {
                std::cout << "    ✗ " << format_pair.second << " -> RGB24 conversion failed" << std::endl;
            }
        }
        g_test_results.add_result("RGA Multiple Format Conversions", passed == formats.size());
    }

    // 测试3: 不同尺寸缩放
    {
        std::cout << "\nTest 3: Different scale ratios" << std::endl;
        Frame src = create_test_frame(1920, 1080, V4L2_PIX_FMT_RGB24);

        std::vector<std::pair<int, int>> target_sizes = {
            {640, 640},   // 缩小 + 裁剪
            {320, 240},   // 缩小
            {1280, 720},  // 缩小
            {960, 540}    // 缩小
        };

        uint32_t passed = 0;
        for (auto& size : target_sizes) {
            Frame dst;
            if (rga.resize(src, dst, size.first, size.second)) {
                if (validate_frame(dst, size.first, size.second, V4L2_PIX_FMT_RGB24)) {
                    std::cout << "    ✓ Resize to " << size.first << "x" << size.second << " successful" << std::endl;
                    passed++;
                } else {
                    std::cout << "    ✗ Resize to " << size.first << "x" << size.second << " validation failed" << std::endl;
                }
            } else {
                std::cout << "    ✗ Resize to " << size.first << "x" << size.second << " failed" << std::endl;
            }
        }
        g_test_results.add_result("RGA Different Scale Ratios", passed == target_sizes.size());
    }

    // 测试4: 裁剪和缩放
    {
        std::cout << "\nTest 4: Crop and resize operations" << std::endl;
        Frame src = create_test_frame(1280, 720, V4L2_PIX_FMT_RGB24);
        Frame dst;

        // 从中心裁剪640x360区域，然后缩放到320x240
        int crop_x = (1280 - 640) / 2;
        int crop_y = (720 - 360) / 2;
        bool success = rga.crop_and_resize(src, dst, crop_x, crop_y, 640, 360, 320, 240);
        bool valid = success && validate_frame(dst, 320, 240, V4L2_PIX_FMT_RGB24);
        g_test_results.add_result("RGA Crop and Resize", valid);
    }

    // 测试5: 边界条件测试
    {
        std::cout << "\nTest 5: Edge cases and boundary conditions" << std::endl;

        // 最小尺寸
        Frame small_src = create_test_frame(16, 16, V4L2_PIX_FMT_RGB24);
        Frame small_dst;
        bool small_success = rga.resize(small_src, small_dst, 32, 32);

        // 奇数尺寸
        Frame odd_src = create_test_frame(101, 101, V4L2_PIX_FMT_RGB24);
        Frame odd_dst;
        bool odd_success = rga.resize(odd_src, odd_dst, 200, 200);

        // 大尺寸
        Frame large_src = create_test_frame(1920, 1080, V4L2_PIX_FMT_RGB24);
        Frame large_dst;
        bool large_success = rga.convert_and_scale(large_src, large_dst, 640, 640, V4L2_PIX_FMT_RGB24);

        bool edge_cases_passed = small_success && odd_success && large_success;
        g_test_results.add_result("RGA Edge Cases", edge_cases_passed);

        if (small_success) std::cout << "    ✓ Small size (16x16) processing successful" << std::endl;
        if (odd_success) std::cout << "    ✓ Odd size (101x101) processing successful" << std::endl;
        if (large_success) std::cout << "    ✓ Large size (1920x1080) processing successful" << std::endl;
    }

    // 测试6: 错误处理测试
    {
        std::cout << "\nTest 6: Error handling" << std::endl;
        bool all_error_tests_passed = true;

        // 子测试1: 测试无效尺寸
        {
            Frame src = create_test_frame(640, 480, V4L2_PIX_FMT_RGB24);
            Frame dst;

            // 测试零目标尺寸
            bool zero_size_result = rga.resize(src, dst, 0, 0);
            if (!zero_size_result) {
                std::cout << "    ✓ Zero target size correctly rejected" << std::endl;
            } else {
                std::cout << "    ✗ Zero target size should have been rejected" << std::endl;
                all_error_tests_passed = false;
            }

            // 测试负目标尺寸
            bool negative_size_result = rga.resize(src, dst, -100, -100);
            if (!negative_size_result) {
                std::cout << "    ✓ Negative target size correctly rejected" << std::endl;
            } else {
                std::cout << "    ✗ Negative target size should have been rejected" << std::endl;
                all_error_tests_passed = false;
            }
        }

        // 子测试2: 测试空数据
        {
            Frame empty_src;
            empty_src.width = 640;
            empty_src.height = 480;
            empty_src.format = V4L2_PIX_FMT_RGB24;
            empty_src.data.clear();  // 空数据

            Frame dst;
            bool empty_data_result = rga.resize(empty_src, dst, 320, 240);
            if (!empty_data_result) {
                std::cout << "    ✓ Empty source data correctly rejected" << std::endl;
            } else {
                std::cout << "    ✗ Empty source data should have been rejected" << std::endl;
                all_error_tests_passed = false;
            }
        }

        // 子测试3: 测试无效格式
        {
            Frame src = create_test_frame(640, 480, 0x12345678);  // 无效格式
            Frame dst;

            bool invalid_format_result = rga.format_convert(src, dst, V4L2_PIX_FMT_RGB24);
            if (!invalid_format_result) {
                std::cout << "    ✓ Invalid source format correctly rejected" << std::endl;
            } else {
                std::cout << "    ⚠ Invalid source format unexpectedly accepted" << std::endl;
                // 不标记为失败，因为某些实现可能有默认处理
            }
        }

        // 子测试4: 测试数据大小不匹配
        {
            Frame src = create_test_frame(640, 480, V4L2_PIX_FMT_RGB24);
            src.data.resize(100);  // 故意设置错误的数据大小

            Frame dst;
            bool size_mismatch_result = rga.resize(src, dst, 320, 240);
            if (!size_mismatch_result) {
                std::cout << "    ✓ Data size mismatch correctly detected" << std::endl;
            } else {
                std::cout << "    ⚠ Data size mismatch not detected" << std::endl;
                // 不标记为失败，因为某些实现可能不检查数据大小
            }
        }

        // 子测试5: 测试极端裁剪参数
        {
            Frame src = create_test_frame(640, 480, V4L2_PIX_FMT_RGB24);
            Frame dst;

            // 测试超出边界的裁剪
            bool out_of_bounds_result = rga.crop_and_resize(src, dst, 700, 500, 100, 100, 50, 50);
            if (!out_of_bounds_result) {
                std::cout << "    ✓ Out-of-bounds crop correctly rejected" << std::endl;
            } else {
                std::cout << "    ✗ Out-of-bounds crop should have been rejected" << std::endl;
                all_error_tests_passed = false;
            }

            // 测试负裁剪坐标
            bool negative_crop_result = rga.crop_and_resize(src, dst, -10, -10, 100, 100, 50, 50);
            if (!negative_crop_result) {
                std::cout << "    ✓ Negative crop coordinates correctly rejected" << std::endl;
            } else {
                std::cout << "    ✗ Negative crop coordinates should have been rejected" << std::endl;
                all_error_tests_passed = false;
            }
        }

        g_test_results.add_result("RGA Error Handling", all_error_tests_passed);

        if (all_error_tests_passed) {
            std::cout << "    ✓ All error handling tests passed" << std::endl;
        } else {
            std::cout << "    ✗ Some error handling tests failed" << std::endl;
        }
    }

    // 测试7: 性能测试
    {
        std::cout << "\nTest 6: Performance benchmark" << std::endl;
        Frame src = create_test_frame(1280, 720, V4L2_PIX_FMT_YUYV);

        const int iterations = 50;
        auto start = std::chrono::high_resolution_clock::now();

        int successful_conversions = 0;
        for (int i = 0; i < iterations; i++) {
            Frame dst;
            if (rga.convert_and_scale(src, dst, 640, 640, V4L2_PIX_FMT_RGB24)) {
                successful_conversions++;
            }
        }

        auto end = std::chrono::high_resolution_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);

        float avg_time = (float)duration.count() / iterations;
        float fps = 1000.0f / avg_time;

        std::cout << "    Performance results:" << std::endl;
        std::cout << "      Iterations: " << iterations << std::endl;
        std::cout << "      Successful: " << successful_conversions << std::endl;
        std::cout << "      Total time: " << duration.count() << " ms" << std::endl;
        std::cout << "      Average time: " << avg_time << " ms/frame" << std::endl;
        std::cout << "      Throughput: " << fps << " fps" << std::endl;

        bool perf_acceptable = (successful_conversions == iterations) && (fps > 30.0f);
        g_test_results.add_result("RGA Performance", perf_acceptable);
    }

    rga.cleanup();
}

void test_mpp_decoder_comprehensive() {
    std::cout << "\n=== Comprehensive MPP Decoder Tests ===" << std::endl;

    // 测试1: 基本初始化
    {
        std::cout << "\nTest 1: MPP Decoder initialization" << std::endl;
        auto mpp = std::make_unique<MPPDecoder>(V4L2_PIX_FMT_MJPEG);
        bool init_success = mpp->init(1280, 720);
        g_test_results.add_result("MPP Initialization", init_success);

        if (init_success) {
            mpp->cleanup();
        } else {
            std::cout << "MPP initialization failed, skipping MPP tests" << std::endl;
            return;
        }
    }

    // 测试2: 不同分辨率初始化
    {
        std::cout << "\nTest 2: Different resolution initialization" << std::endl;
        std::vector<std::pair<int, int>> resolutions = {
            {640, 480},
            {1280, 720},
            {1920, 1080},
            {320, 240}
        };

        uint32_t passed = 0;
        for (auto& res : resolutions) {
            auto mpp = std::make_unique<MPPDecoder>(V4L2_PIX_FMT_MJPEG);
            if (mpp->init(res.first, res.second)) {
                std::cout << "    ✓ " << res.first << "x" << res.second << " initialization successful" << std::endl;
                mpp->cleanup();
                passed++;
            } else {
                std::cout << "    ✗ " << res.first << "x" << res.second << " initialization failed" << std::endl;
            }
        }
        g_test_results.add_result("MPP Different Resolutions", passed == resolutions.size());
    }

    // 测试3: MJPEG解码测试（模拟数据）
    {
        std::cout << "\nTest 3: MJPEG decoding (simulated data)" << std::endl;
        auto mpp = std::make_unique<MPPDecoder>(V4L2_PIX_FMT_MJPEG);
        if (mpp->init(1280, 720)) {
            // 创建模拟MJPEG数据
            Frame src = create_test_frame(1280, 720, V4L2_PIX_FMT_MJPEG);
            Frame dst;

            // 注意：这里使用模拟数据，实际解码可能会失败，这是正常的
            bool decode_success = mpp->decode_frame(src, dst);

            // 对于模拟数据，我们主要测试函数调用不会崩溃
            std::cout << "    MJPEG decode attempt completed (result: "
                      << (decode_success ? "success" : "failed") << ")" << std::endl;
            std::cout << "    Note: Failure is expected with simulated MJPEG data" << std::endl;

            mpp->cleanup();
            // 对于模拟数据测试，我们认为不崩溃就是成功
            g_test_results.add_result("MPP MJPEG Decode (Simulated)", true);
        } else {
            g_test_results.add_result("MPP MJPEG Decode (Simulated)", false);
        }
    }

    // 测试4: 错误处理测试
    {
        std::cout << "\nTest 4: Error handling" << std::endl;
        bool all_error_tests_passed = true;

        // 子测试1: 测试未初始化的解码器
        {
            auto mpp = std::make_unique<MPPDecoder>(V4L2_PIX_FMT_MJPEG);
            Frame invalid_src = create_test_frame(640, 480, V4L2_PIX_FMT_MJPEG);
            Frame dst;

            // 未初始化的解码器应该能够自动初始化或返回false
            bool uninit_result = mpp->decode_frame(invalid_src, dst);

            // 注意：根据实现，decode_frame可能会自动初始化，所以这里不强制要求返回false
            std::cout << "    Uninitialized decoder decode result: "
                      << (uninit_result ? "auto-initialized and succeeded" : "failed as expected") << std::endl;

            mpp->cleanup();
        }

        // 子测试2: 测试无效格式初始化
        {
            auto mpp = std::make_unique<MPPDecoder>(V4L2_PIX_FMT_RGB24);
            bool invalid_format_result = mpp->init(640, 480);

            if (!invalid_format_result) {
                std::cout << "    ✓ Invalid format (RGB24) correctly rejected" << std::endl;
            } else {
                std::cout << "    ✗ Invalid format (RGB24) should have been rejected" << std::endl;
                all_error_tests_passed = false;
            }

            mpp->cleanup();
        }

        // 子测试3: 测试空数据解码
        {
            auto mpp = std::make_unique<MPPDecoder>(V4L2_PIX_FMT_MJPEG);
            if (mpp->init(640, 480)) {
                Frame empty_src;
                empty_src.width = 640;
                empty_src.height = 480;
                empty_src.format = V4L2_PIX_FMT_MJPEG;
                empty_src.data.clear();  // 空数据

                Frame dst;
                bool empty_decode_result = mpp->decode_frame(empty_src, dst);

                if (!empty_decode_result) {
                    std::cout << "    ✓ Empty data decode correctly failed" << std::endl;
                } else {
                    std::cout << "    ✗ Empty data decode should have failed" << std::endl;
                    all_error_tests_passed = false;
                }

                mpp->cleanup();
            } else {
                std::cout << "    ✗ Failed to initialize MPP for empty data test" << std::endl;
                all_error_tests_passed = false;
            }
        }

        // 子测试4: 测试无效尺寸初始化
        {
            auto mpp = std::make_unique<MPPDecoder>(V4L2_PIX_FMT_MJPEG);

            // 测试零尺寸
            bool zero_size_result = mpp->init(0, 0);
            if (!zero_size_result) {
                std::cout << "    ✓ Zero size initialization correctly rejected" << std::endl;
            } else {
                std::cout << "    ⚠ Zero size initialization unexpectedly succeeded" << std::endl;
                // 不标记为失败，因为某些实现可能允许这种情况
            }

            // 测试负尺寸
            bool negative_size_result = mpp->init(-1, -1);
            if (!negative_size_result) {
                std::cout << "    ✓ Negative size initialization correctly rejected" << std::endl;
            } else {
                std::cout << "    ⚠ Negative size initialization unexpectedly succeeded" << std::endl;
                // 不标记为失败，因为某些实现可能允许这种情况
            }

            mpp->cleanup();
        }

        // 子测试5: 测试重复初始化
        {
            auto mpp = std::make_unique<MPPDecoder>(V4L2_PIX_FMT_MJPEG);
            bool first_init = mpp->init(640, 480);
            bool second_init = mpp->init(1280, 720);

            if (first_init) {
                std::cout << "    ✓ First initialization succeeded" << std::endl;
                if (second_init) {
                    std::cout << "    ✓ Second initialization succeeded (re-initialization allowed)" << std::endl;
                } else {
                    std::cout << "    ⚠ Second initialization failed (re-initialization not allowed)" << std::endl;
                    // 不标记为失败，因为某些实现可能不允许重复初始化
                }
            } else {
                std::cout << "    ✗ First initialization failed" << std::endl;
                all_error_tests_passed = false;
            }

            mpp->cleanup();
        }

        g_test_results.add_result("MPP Error Handling", all_error_tests_passed);

        if (all_error_tests_passed) {
            std::cout << "    ✓ All error handling tests passed" << std::endl;
        } else {
            std::cout << "    ✗ Some error handling tests failed" << std::endl;
        }
    }

    // 测试5: 多次初始化和清理
    {
        std::cout << "\nTest 5: Multiple init/cleanup cycles" << std::endl;
        bool all_cycles_passed = true;

        for (int i = 0; i < 5; i++) {
            auto mpp = std::make_unique<MPPDecoder>(V4L2_PIX_FMT_MJPEG);
            if (!mpp->init(640, 480)) {
                all_cycles_passed = false;
                break;
            }
            mpp->cleanup();
        }

        g_test_results.add_result("MPP Multiple Init/Cleanup", all_cycles_passed);
        if (all_cycles_passed) {
            std::cout << "    ✓ All 5 init/cleanup cycles successful" << std::endl;
        }
    }
}

void test_gstreamer_codec_comprehensive() {
    std::cout << "\n=== Comprehensive GStreamer Encoder Tests ===" << std::endl;

    // 测试1: H264编码器初始化
    {
        std::cout << "\nTest 1: H264 encoder initialization" << std::endl;
        GStreamerEncoder h264_encoder(EncoderType::H264, 2000000);
        bool init_success = h264_encoder.init();
        g_test_results.add_result("GStreamer H264 Initialization", init_success);

        if (init_success) {
            h264_encoder.cleanup();
        } else {
            std::cout << "GStreamer H264 initialization failed, skipping H264 tests" << std::endl;
        }
    }

    // 测试2: H264编码测试
    {
        std::cout << "\nTest 2: H264 encoding" << std::endl;
        GStreamerEncoder h264_encoder(EncoderType::H264, 2000000);
        if (h264_encoder.init()) {
            Frame src = create_test_frame(640, 480, V4L2_PIX_FMT_RGB24);
            Frame dst;

            bool encode_success = h264_encoder.encode(src, dst);
            bool valid = encode_success && !dst.data.empty();

            g_test_results.add_result("GStreamer H264 Encoding", valid);

            if (valid) {
                std::cout << "    ✓ H264 encoding successful: " << dst.data.size() << " bytes" << std::endl;
            }

            h264_encoder.cleanup();
        } else {
            g_test_results.add_result("GStreamer H264 Encoding", false);
        }
    }

    // 测试3: 不同编码器类型测试
    {
        std::cout << "\nTest 3: Different encoder types" << std::endl;

        // 测试H264编码器
        {
            std::cout << "  Testing H264 encoder..." << std::endl;
            GStreamerEncoder h264_encoder(EncoderType::H264, 2000000);
            bool h264_success = false;
            if (h264_encoder.init()) {
                Frame src = create_test_frame(640, 480, V4L2_PIX_FMT_RGB24);
                Frame dst;
                h264_success = h264_encoder.encode(src, dst) && !dst.data.empty();
                if (h264_success) {
                    std::cout << "    ✓ H264 encoding successful: " << dst.data.size() << " bytes" << std::endl;
                }
                h264_encoder.cleanup();
            }

            // 测试H265编码器
            std::cout << "  Testing H265 encoder..." << std::endl;
            GStreamerEncoder h265_encoder(EncoderType::H265, 2000000);
            bool h265_success = false;
            if (h265_encoder.init()) {
                Frame src = create_test_frame(640, 480, V4L2_PIX_FMT_RGB24);
                Frame dst;
                h265_success = h265_encoder.encode(src, dst) && !dst.data.empty();
                if (h265_success) {
                    std::cout << "    ✓ H265 encoding successful: " << dst.data.size() << " bytes" << std::endl;
                }
                h265_encoder.cleanup();
            }

            // 测试JPEG编码器
            std::cout << "  Testing JPEG encoder..." << std::endl;
            GStreamerEncoder jpeg_encoder(EncoderType::JPEG, 85);
            bool jpeg_success = false;
            if (jpeg_encoder.init()) {
                Frame src = create_test_frame(640, 480, V4L2_PIX_FMT_RGB24);
                Frame dst;
                jpeg_success = jpeg_encoder.encode(src, dst) && !dst.data.empty();
                if (jpeg_success) {
                    std::cout << "    ✓ JPEG encoding successful: " << dst.data.size() << " bytes" << std::endl;
                }
                jpeg_encoder.cleanup();
            }

            g_test_results.add_result("GStreamer Different Encoder Types",
                                    h264_success && h265_success && jpeg_success);
        }
    }

    // 测试4: 不同比特率编码器
    {
        std::cout << "\nTest 4: Different bitrate encoders" << std::endl;
        std::vector<int> bitrates = {500000, 1000000, 2000000, 4000000};

        uint32_t passed = 0;
        Frame src = create_test_frame(640, 480, V4L2_PIX_FMT_RGB24);

        for (int bitrate : bitrates) {
            GStreamerEncoder encoder(EncoderType::H264, bitrate);
            if (encoder.init()) {
                Frame dst;
                if (encoder.encode(src, dst) && !dst.data.empty()) {
                    std::cout << "    ✓ Bitrate " << bitrate/1000 << "k encoding successful: "
                              << dst.data.size() << " bytes" << std::endl;
                    passed++;
                } else {
                    std::cout << "    ✗ Bitrate " << bitrate/1000 << "k encoding failed" << std::endl;
                }
                encoder.cleanup();
            }
        }

        g_test_results.add_result("GStreamer Different Bitrates", passed == bitrates.size());
    }

    // 测试5: 多帧编码测试
    {
        std::cout << "\nTest 5: Multiple frame encoding" << std::endl;
        GStreamerEncoder encoder(EncoderType::H264, 2000000);
        if (encoder.init()) {
            const int frame_count = 5;
            int successful_encodes = 0;

            for (int i = 0; i < frame_count; i++) {
                Frame src = create_test_frame(640, 480, V4L2_PIX_FMT_RGB24, i + 1);
                Frame dst;

                if (encoder.encode(src, dst) && !dst.data.empty()) {
                    successful_encodes++;
                }
            }

            encoder.cleanup();

            bool all_successful = (successful_encodes == frame_count);
            g_test_results.add_result("GStreamer Multiple Frame Encoding", all_successful);

            std::cout << "    Successful encodes: " << successful_encodes << "/" << frame_count << std::endl;
        } else {
            g_test_results.add_result("GStreamer Multiple Frame Encoding", false);
        }
    }

    // 测试6: 错误处理测试
    {
        std::cout << "\nTest 6: Error handling" << std::endl;
        bool all_error_tests_passed = true;

        // 子测试1: 测试未初始化的编码器
        {
            GStreamerEncoder encoder(EncoderType::H264, 2000000);
            Frame src = create_test_frame(640, 480, V4L2_PIX_FMT_RGB24);
            Frame dst;
            bool uninit_result = encoder.encode(src, dst);

            if (!uninit_result) {
                std::cout << "    ✓ Uninitialized encoder correctly failed" << std::endl;
            } else {
                std::cout << "    ✗ Uninitialized encoder should have failed" << std::endl;
                all_error_tests_passed = false;
            }
        }

        // 子测试2: 测试无效数据编码
        {
            GStreamerEncoder gst(EncoderType::H264, 2000000);
            if (gst.init()) {
                Frame invalid_src;
                invalid_src.width = 640;
                invalid_src.height = 480;
                invalid_src.format = V4L2_PIX_FMT_RGB24;
                invalid_src.data.clear();  // 空数据

                Frame dst;
                bool empty_result = gst.encode(invalid_src, dst);

                if (!empty_result) {
                    std::cout << "    ✓ Empty data encoding correctly failed" << std::endl;
                } else {
                    std::cout << "    ✗ Empty data encoding should have failed" << std::endl;
                    all_error_tests_passed = false;
                }

                gst.cleanup();
            } else {
                std::cout << "    ✗ Failed to initialize GStreamer for empty data test" << std::endl;
                all_error_tests_passed = false;
            }
        }

        // 子测试3: 测试无效比特率
        {
            GStreamerEncoder gst(EncoderType::H264, 2000000);
            if (gst.init()) {
                Frame src = create_test_frame(640, 480, V4L2_PIX_FMT_RGB24);
                Frame dst;

                // 测试零比特率
                bool zero_bitrate_result = gst.encode(src, dst);
                if (!zero_bitrate_result) {
                    std::cout << "    ✓ Zero bitrate encoding correctly failed" << std::endl;
                } else {
                    std::cout << "    ✗ Zero bitrate encoding should have failed" << std::endl;
                    all_error_tests_passed = false;
                }

                // 测试负比特率
                bool negative_bitrate_result = gst.encode(src, dst);
                if (!negative_bitrate_result) {
                    std::cout << "    ✓ Negative bitrate encoding correctly failed" << std::endl;
                } else {
                    std::cout << "    ✗ Negative bitrate encoding should have failed" << std::endl;
                    all_error_tests_passed = false;
                }

                gst.cleanup();
            } else {
                std::cout << "    ✗ Failed to initialize GStreamer for bitrate test" << std::endl;
                all_error_tests_passed = false;
            }
        }

        // 子测试4: 测试无效帧格式
        {
            GStreamerEncoder gst(EncoderType::H264, 2000000);
            if (gst.init()) {
                Frame invalid_format_src = create_test_frame(640, 480, V4L2_PIX_FMT_MJPEG);  // 不支持的输入格式
                Frame dst;

                bool invalid_format_result = gst.encode(invalid_format_src, dst);
                if (!invalid_format_result) {
                    std::cout << "    ✓ Invalid input format correctly rejected" << std::endl;
                } else {
                    std::cout << "    ⚠ Invalid input format unexpectedly accepted" << std::endl;
                    // 不标记为失败，因为某些实现可能支持格式转换
                }

                gst.cleanup();
            } else {
                std::cout << "    ✗ Failed to initialize GStreamer for format test" << std::endl;
                all_error_tests_passed = false;
            }
        }

        // 子测试5: 测试无效尺寸
        {
            GStreamerEncoder gst(EncoderType::H264, 2000000);
            if (gst.init()) {
                Frame zero_size_src = create_test_frame(0, 0, V4L2_PIX_FMT_RGB24);
                Frame dst;

                bool zero_size_result = gst.encode(zero_size_src, dst);
                if (!zero_size_result) {
                    std::cout << "    ✓ Zero size frame correctly rejected" << std::endl;
                } else {
                    std::cout << "    ✗ Zero size frame should have been rejected" << std::endl;
                    all_error_tests_passed = false;
                }

                gst.cleanup();
            } else {
                std::cout << "    ✗ Failed to initialize GStreamer for size test" << std::endl;
                all_error_tests_passed = false;
            }
        }

        g_test_results.add_result("GStreamer Error Handling", all_error_tests_passed);

        if (all_error_tests_passed) {
            std::cout << "    ✓ All error handling tests passed" << std::endl;
        } else {
            std::cout << "    ✗ Some error handling tests failed" << std::endl;
        }
    }

    // 测试7: 性能测试
    {
        std::cout << "\nTest 7: Encoding performance" << std::endl;
        GStreamerEncoder gst(EncoderType::H264, 2000000);
        if (gst.init()) {
            Frame src = create_test_frame(1280, 720, V4L2_PIX_FMT_RGB24);

            const int iterations = 20;
            auto start = std::chrono::high_resolution_clock::now();

            int successful_encodes = 0;
            size_t total_output_size = 0;

            for (int i = 0; i < iterations; i++) {
                Frame dst;
                if (gst.encode(src, dst) && !dst.data.empty()) {
                    successful_encodes++;
                    total_output_size += dst.data.size();
                }
            }

            auto end = std::chrono::high_resolution_clock::now();
            auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(end - start);

            float avg_time = (float)duration.count() / iterations;
            float fps = 1000.0f / avg_time;
            float avg_output_size = (float)total_output_size / successful_encodes;

            std::cout << "    Performance results:" << std::endl;
            std::cout << "      Iterations: " << iterations << std::endl;
            std::cout << "      Successful: " << successful_encodes << std::endl;
            std::cout << "      Total time: " << duration.count() << " ms" << std::endl;
            std::cout << "      Average time: " << avg_time << " ms/frame" << std::endl;
            std::cout << "      Throughput: " << fps << " fps" << std::endl;
            std::cout << "      Average output size: " << avg_output_size << " bytes" << std::endl;

            bool perf_acceptable = (successful_encodes == iterations) && (fps > 10.0f);
            g_test_results.add_result("GStreamer Performance", perf_acceptable);

            gst.cleanup();
        } else {
            g_test_results.add_result("GStreamer Performance", false);
        }
    }
}

void test_video_converter_switches() {
    std::cout << "Testing VideoConverter processing switches..." << std::endl;

    try {
        // 测试1: 只启用AI处理
        {
            std::cout << "Test 1: AI only" << std::endl;
            VideoConverter converter;
            VideoConverterConfig config;
            config.enable_ai = true;
            config.enable_cloud_streaming = false;
            config.ai_width = 640;
            config.ai_height = 640;

            converter.set_config(config);

            if (converter.init("Test_Input", "Test_AI_Output", "Test_Cloud_Output")) {
                std::cout << "✓ AI-only converter initialized successfully" << std::endl;
            } else {
                std::cout << "✗ AI-only converter initialization failed" << std::endl;
            }
        }

        // 测试2: 只启用云端流媒体
        {
            std::cout << "Test 2: Cloud streaming only" << std::endl;
            VideoConverter converter;
            VideoConverterConfig config;
            config.enable_ai = false;
            config.enable_cloud_streaming = true;
            config.cloud_bitrate = 2000000;

            converter.set_config(config);

            if (converter.init("Test_Input2", "Test_AI_Output2", "Test_Cloud_Output2")) {
                std::cout << "✓ Cloud-only converter initialized successfully" << std::endl;
            } else {
                std::cout << "✗ Cloud-only converter initialization failed" << std::endl;
            }
        }

        // 测试3: 全部禁用
        {
            std::cout << "Test 3: All processing disabled" << std::endl;
            VideoConverter converter;
            VideoConverterConfig config;
            config.enable_ai = false;
            config.enable_cloud_streaming = false;

            converter.set_config(config);

            if (converter.init("Test_Input3", "Test_AI_Output3", "Test_Cloud_Output3")) {
                std::cout << "✓ Disabled converter initialized successfully" << std::endl;
                std::cout << "  Note: This configuration will drop all frames" << std::endl;
            } else {
                std::cout << "✗ Disabled converter initialization failed" << std::endl;
            }
        }

        std::cout << "✓ All switch tests completed" << std::endl;

    } catch (const std::exception& e) {
        std::cerr << "Switch test failed with exception: " << e.what() << std::endl;
    }
}

void test_config_loader() {
    std::cout << "\n=== Configuration Loader Tests ===" << std::endl;

    // 测试1: 默认配置加载
    {
        std::cout << "\nTest 1: Default configuration loading" << std::endl;
        VideoConverterConfig config;
        std::string input_topic, ai_topic, cloud_topic;

        // 尝试加载默认配置文件
        bool load_success = ConfigLoader::load_video_converter_config(
            "config/video_converter.json", config, input_topic, ai_topic, cloud_topic);

        g_test_results.add_result("Config Loader Default", load_success);

        if (load_success) {
            std::cout << "    ✓ Default configuration loaded successfully" << std::endl;
            std::cout << "      Input topic: " << input_topic << std::endl;
            std::cout << "      AI topic: " << ai_topic << std::endl;
            std::cout << "      Cloud topic: " << cloud_topic << std::endl;
            std::cout << "      Enable AI: " << (config.enable_ai ? "true" : "false") << std::endl;
            std::cout << "      Enable Cloud: " << (config.enable_cloud_streaming ? "true" : "false") << std::endl;
        }
    }

    // 测试2: 不存在的配置文件
    {
        std::cout << "\nTest 2: Non-existent configuration file" << std::endl;
        VideoConverterConfig config;
        std::string input_topic, ai_topic, cloud_topic;

        bool load_success = ConfigLoader::load_video_converter_config(
            "non_existent_config.json", config, input_topic, ai_topic, cloud_topic);

        // 应该失败但不崩溃
        g_test_results.add_result("Config Loader Non-existent", !load_success);

        if (!load_success) {
            std::cout << "    ✓ Non-existent file handled correctly" << std::endl;
        }
    }

    // 测试3: 创建临时配置文件测试
    {
        std::cout << "\nTest 3: Custom configuration file" << std::endl;

        // 创建临时配置文件
        std::string temp_config = R"({
  "video_converter": {
    "enable_hardware_acceleration": false,
    "processing_control": {
      "enable_ai": false,
      "enable_cloud_streaming": true
    },
    "dds": {
      "input_topic": "Custom_Input",
      "ai_output_topic": "Custom_AI",
      "cloud_output_topic": "Custom_Cloud"
    },
    "ai_output": {
      "width": 320,
      "height": 240
    },
    "cloud_output": {
      "bitrate": 1000000
    }
  }
})";

        std::ofstream temp_file("temp_test_config.json");
        if (temp_file.is_open()) {
            temp_file << temp_config;
            temp_file.close();

            VideoConverterConfig config;
            std::string input_topic, ai_topic, cloud_topic;

            bool load_success = ConfigLoader::load_video_converter_config(
                "temp_test_config.json", config, input_topic, ai_topic, cloud_topic);

            bool config_correct = load_success &&
                                !config.enable_hardware_acceleration &&
                                !config.enable_ai &&
                                config.enable_cloud_streaming &&
                                (input_topic == "Custom_Input") &&
                                (ai_topic == "Custom_AI") &&
                                (cloud_topic == "Custom_Cloud") &&
                                (config.ai_width == 320) &&
                                (config.ai_height == 240) &&
                                (config.cloud_bitrate == 1000000);

            g_test_results.add_result("Config Loader Custom", config_correct);

            if (config_correct) {
                std::cout << "    ✓ Custom configuration loaded and parsed correctly" << std::endl;
            }

            // 清理临时文件
            std::remove("temp_test_config.json");
        } else {
            g_test_results.add_result("Config Loader Custom", false);
            std::cout << "    ✗ Failed to create temporary config file" << std::endl;
        }
    }

    // 测试4: 错误处理测试
    {
        std::cout << "\nTest 4: Configuration error handling" << std::endl;
        bool all_error_tests_passed = true;

        // 子测试1: 测试无效JSON格式
        {
            std::string invalid_json = R"({
  "video_converter": {
    "enable_hardware_acceleration": false,
    "processing_control": {
      "enable_ai": false,
      "enable_cloud_streaming": true
    },
    "dds": {
      "input_topic": "Test_Input"
      // 缺少逗号，无效JSON
      "ai_output_topic": "Test_AI"
    }
  }
})";

            std::ofstream invalid_file("invalid_test_config.json");
            if (invalid_file.is_open()) {
                invalid_file << invalid_json;
                invalid_file.close();

                VideoConverterConfig config;
                std::string input_topic, ai_topic, cloud_topic;

                bool invalid_json_result = ConfigLoader::load_video_converter_config(
                    "invalid_test_config.json", config, input_topic, ai_topic, cloud_topic);

                if (!invalid_json_result) {
                    std::cout << "    ✓ Invalid JSON format correctly rejected" << std::endl;
                } else {
                    std::cout << "    ✗ Invalid JSON format should have been rejected" << std::endl;
                    all_error_tests_passed = false;
                }

                std::remove("invalid_test_config.json");
            }
        }

        // 子测试2: 测试缺少必需字段
        {
            std::string incomplete_config = R"({
  "video_converter": {
    "enable_hardware_acceleration": true
    // 缺少其他必需字段
  }
})";

            std::ofstream incomplete_file("incomplete_test_config.json");
            if (incomplete_file.is_open()) {
                incomplete_file << incomplete_config;
                incomplete_file.close();

                VideoConverterConfig config;
                std::string input_topic, ai_topic, cloud_topic;

                bool incomplete_result = ConfigLoader::load_video_converter_config(
                    "incomplete_test_config.json", config, input_topic, ai_topic, cloud_topic);

                if (!incomplete_result) {
                    std::cout << "    ✓ Incomplete configuration correctly rejected" << std::endl;
                } else {
                    std::cout << "    ⚠ Incomplete configuration accepted (may have defaults)" << std::endl;
                    // 不标记为失败，因为某些实现可能有默认值
                }

                std::remove("incomplete_test_config.json");
            }
        }

        // 子测试3: 测试无效数值
        {
            std::string invalid_values_config = R"({
  "video_converter": {
    "enable_hardware_acceleration": true,
    "processing_control": {
      "enable_ai": true,
      "enable_cloud_streaming": true
    },
    "dds": {
      "input_topic": "Test_Input",
      "ai_output_topic": "Test_AI",
      "cloud_output_topic": "Test_Cloud"
    },
    "ai_output": {
      "width": -100,
      "height": 0
    },
    "cloud_output": {
      "bitrate": -1000000
    }
  }
})";

            std::ofstream invalid_values_file("invalid_values_config.json");
            if (invalid_values_file.is_open()) {
                invalid_values_file << invalid_values_config;
                invalid_values_file.close();

                VideoConverterConfig config;
                std::string input_topic, ai_topic, cloud_topic;

                bool invalid_values_result = ConfigLoader::load_video_converter_config(
                    "invalid_values_config.json", config, input_topic, ai_topic, cloud_topic);

                if (invalid_values_result) {
                    // 检查是否正确处理了无效值
                    if (config.ai_width <= 0 || config.ai_height <= 0 || config.cloud_bitrate <= 0) {
                        std::cout << "    ✗ Invalid values were not corrected" << std::endl;
                        all_error_tests_passed = false;
                    } else {
                        std::cout << "    ✓ Invalid values were corrected to valid defaults" << std::endl;
                    }
                } else {
                    std::cout << "    ✓ Invalid values correctly caused configuration rejection" << std::endl;
                }

                std::remove("invalid_values_config.json");
            }
        }

        // 子测试4: 测试空文件
        {
            std::ofstream empty_file("empty_config.json");
            if (empty_file.is_open()) {
                empty_file.close();  // 创建空文件

                VideoConverterConfig config;
                std::string input_topic, ai_topic, cloud_topic;

                bool empty_file_result = ConfigLoader::load_video_converter_config(
                    "empty_config.json", config, input_topic, ai_topic, cloud_topic);

                if (!empty_file_result) {
                    std::cout << "    ✓ Empty configuration file correctly rejected" << std::endl;
                } else {
                    std::cout << "    ✗ Empty configuration file should have been rejected" << std::endl;
                    all_error_tests_passed = false;
                }

                std::remove("empty_config.json");
            }
        }

        // 子测试5: 测试权限问题（只读文件）
        {
            // 注意：这个测试在某些系统上可能不适用
            std::cout << "    ⚠ Skipping permission test (platform dependent)" << std::endl;
        }

        g_test_results.add_result("Config Loader Error Handling", all_error_tests_passed);

        if (all_error_tests_passed) {
            std::cout << "    ✓ All configuration error handling tests passed" << std::endl;
        } else {
            std::cout << "    ✗ Some configuration error handling tests failed" << std::endl;
        }
    }
}

void test_stress_and_performance() {
    std::cout << "\n=== Stress and Performance Tests ===" << std::endl;

    // 测试1: 内存泄漏测试
    {
        std::cout << "\nTest 1: Memory leak test (multiple init/cleanup cycles)" << std::endl;

        bool all_cycles_passed = true;
        const int cycles = 10;

        for (int i = 0; i < cycles; i++) {
            try {
                VideoConverter converter;
                VideoConverterConfig config;
                config.enable_ai = (i % 2 == 0);  // 交替启用AI
                config.enable_cloud_streaming = (i % 3 == 0);  // 交替启用云端
                config.ai_width = 640;
                config.ai_height = 640;
                config.cloud_bitrate = 2000000;

                converter.set_config(config);

                if (converter.init("Stress_Input_" + std::to_string(i),
                                 "Stress_AI_" + std::to_string(i),
                                 "Stress_Cloud_" + std::to_string(i))) {
                    converter.start();
                    std::this_thread::sleep_for(std::chrono::milliseconds(50));
                    converter.stop();
                } else {
                    all_cycles_passed = false;
                    break;
                }
            } catch (const std::exception& e) {
                std::cout << "    ✗ Cycle " << i << " failed: " << e.what() << std::endl;
                all_cycles_passed = false;
                break;
            }
        }

        g_test_results.add_result("Stress Memory Leak", all_cycles_passed);

        if (all_cycles_passed) {
            std::cout << "    ✓ All " << cycles << " init/cleanup cycles completed successfully" << std::endl;
        }
    }

    // 测试2: 并发测试
    {
        std::cout << "\nTest 2: Concurrent VideoConverter instances" << std::endl;

        const int instance_count = 3;
        std::vector<std::unique_ptr<VideoConverter>> converters;
        std::vector<std::thread> threads;
        std::atomic<int> successful_inits(0);

        // 创建多个实例
        for (int i = 0; i < instance_count; i++) {
            threads.emplace_back([&, i]() {
                try {
                    auto converter = std::make_unique<VideoConverter>();
                    VideoConverterConfig config;
                    config.enable_ai = true;
                    config.enable_cloud_streaming = false;  // 简化测试
                    config.ai_width = 640;
                    config.ai_height = 640;

                    converter->set_config(config);

                    if (converter->init("Concurrent_Input_" + std::to_string(i),
                                      "Concurrent_AI_" + std::to_string(i),
                                      "Concurrent_Cloud_" + std::to_string(i))) {
                        converter->start();
                        std::this_thread::sleep_for(std::chrono::milliseconds(200));
                        converter->stop();
                        successful_inits.fetch_add(1);
                    }
                } catch (const std::exception& e) {
                    std::cout << "    ✗ Concurrent instance " << i << " failed: " << e.what() << std::endl;
                }
            });
        }

        // 等待所有线程完成
        for (auto& thread : threads) {
            thread.join();
        }

        bool concurrent_success = (successful_inits.load() == instance_count);
        g_test_results.add_result("Stress Concurrent Instances", concurrent_success);

        std::cout << "    Successful concurrent instances: " << successful_inits.load()
                  << "/" << instance_count << std::endl;
    }

    // 测试3: 长时间运行测试
    {
        std::cout << "\nTest 3: Long running stability test" << std::endl;

        try {
            VideoConverter converter;
            VideoConverterConfig config;
            config.enable_ai = true;
            config.enable_cloud_streaming = true;
            config.ai_width = 640;
            config.ai_height = 640;
            config.cloud_bitrate = 2000000;

            converter.set_config(config);

            if (converter.init("LongRun_Input", "LongRun_AI", "LongRun_Cloud")) {
                converter.start();

                // 运行5秒并定期检查统计信息
                const int duration_seconds = 5;
                const int check_interval_ms = 500;
                const int checks = (duration_seconds * 1000) / check_interval_ms;

                bool stability_ok = true;
                VideoConverter::Stats prev_stats = {};

                for (int i = 0; i < checks; i++) {
                    std::this_thread::sleep_for(std::chrono::milliseconds(check_interval_ms));

                    VideoConverter::Stats stats;
                    converter.get_stats(stats);

                    // 检查统计信息是否合理
                    if (stats.frames_processed < prev_stats.frames_processed ||
                        stats.frames_dropped < prev_stats.frames_dropped ||
                        stats.ai_frames_sent < prev_stats.ai_frames_sent ||
                        stats.cloud_frames_sent < prev_stats.cloud_frames_sent) {
                        stability_ok = false;
                        break;
                    }

                    prev_stats = stats;
                }

                converter.stop();

                g_test_results.add_result("Stress Long Running", stability_ok);

                if (stability_ok) {
                    std::cout << "    ✓ " << duration_seconds << " second stability test passed" << std::endl;
                    std::cout << "      Final stats - Processed: " << prev_stats.frames_processed
                              << ", Dropped: " << prev_stats.frames_dropped << std::endl;
                }
            } else {
                g_test_results.add_result("Stress Long Running", false);
            }
        } catch (const std::exception& e) {
            std::cout << "    ✗ Long running test exception: " << e.what() << std::endl;
            g_test_results.add_result("Stress Long Running", false);
        }
    }
}

int main(int argc, char* argv[]) {
    std::cout << "=== Comprehensive Video Converter Test Suite ===" << std::endl;
    std::cout << "Version: 2.0 - Enhanced with comprehensive testing" << std::endl;
    std::cout << std::endl;

    // 解析命令行参数
    bool run_stress_tests = false;
    bool verbose = false;

    for (int i = 1; i < argc; i++) {
        std::string arg = argv[i];
        if (arg == "--stress") {
            run_stress_tests = true;
        } else if (arg == "--verbose" || arg == "-v") {
            verbose = true;
        } else if (arg == "--help" || arg == "-h") {
            std::cout << "Usage: " << argv[0] << " [OPTIONS]" << std::endl;
            std::cout << "Options:" << std::endl;
            std::cout << "  --stress    Run stress and performance tests" << std::endl;
            std::cout << "  --verbose   Enable verbose logging" << std::endl;
            std::cout << "  --help      Show this help message" << std::endl;
            return 0;
        }
    }

    // 设置日志级别
    if (verbose) {
        Logger::set_level(LEVEL_DEBUG);
        std::cout << "Verbose logging enabled" << std::endl;
    } else {
        Logger::set_level(LEVEL_INFO);
    }

    auto start_time = std::chrono::high_resolution_clock::now();

    std::cout << "Starting test execution..." << std::endl;
    std::cout << std::endl;

    // 运行核心组件测试
    std::cout << "🔧 CORE COMPONENT TESTS" << std::endl;
    test_rga_accelerator_comprehensive();
    test_mpp_decoder_comprehensive();
    test_gstreamer_codec_comprehensive();

    // 运行VideoConverter测试
    std::cout << "\n🎯 VIDEO CONVERTER TESTS" << std::endl;
    test_video_converter_comprehensive();
    test_video_converter_switches();

    // 运行配置测试
    std::cout << "\n⚙️ CONFIGURATION TESTS" << std::endl;
    test_config_loader();

    // 运行压力测试（可选）
    if (run_stress_tests) {
        std::cout << "\n🚀 STRESS AND PERFORMANCE TESTS" << std::endl;
        test_stress_and_performance();
    } else {
        std::cout << "\n⏭️ Skipping stress tests (use --stress to enable)" << std::endl;
    }

    auto end_time = std::chrono::high_resolution_clock::now();
    auto duration = std::chrono::duration_cast<std::chrono::seconds>(end_time - start_time);

    std::cout << "\n" << std::string(60, '=') << std::endl;
    std::cout << "TEST EXECUTION COMPLETED" << std::endl;
    std::cout << "Total execution time: " << duration.count() << " seconds" << std::endl;
    std::cout << std::endl;

    // 打印测试结果摘要
    g_test_results.print_summary();

    std::cout << std::endl;

    // 返回适当的退出码
    if (g_test_results.failed_tests == 0) {
        std::cout << "🎉 ALL TESTS PASSED! 🎉" << std::endl;
        return 0;
    } else {
        std::cout << "❌ SOME TESTS FAILED ❌" << std::endl;
        std::cout << "Please check the failed tests above for details." << std::endl;
        return 1;
    }
}
