# Dual Video Control Service Testing

## 概述

本测试套件专门用于测试双路视频控制服务，支持可见光和红外摄像头的同时拍照和录像功能。

## 测试配置

### 视频流配置
- **可见光流**: `main_video_frames` (1920x1080@25fps, 4Mbps H265)
- **红外流**: `thermal_video_frames` (640x512@30fps, 1Mbps H265)
- **分段时长**: 5分钟自动分段
- **测试时长**: 30分钟完整录制测试

### 文件命名规则
```
照片: photo_visible_YYYYMMDD_HHMMSS_mmm.jpg
     photo_infrared_YYYYMMDD_HHMMSS_mmm.jpg

视频: video_visible_[session]_YYYYMMDD_HHMMSS_seg001.mp4
     video_infrared_[session]_YYYYMMDD_HHMMSS_seg001.mp4
```

## 测试文件

### 1. 单元测试 (`test_video_control.cpp`)
```cpp
// 主要测试功能
- 双路配置加载测试
- 双路DDS连接测试  
- 双路拍照模拟测试
- 双路录像模拟测试
- 30分钟录制计算测试
- 服务生命周期测试
```

### 2. 测试配置 (`test_dual_video_config.json`)
```json
{
  "dual_video_streams": {
    "visible_stream": {
      "dds_topic": "main_video_frames",
      "width": 1920, "height": 1080, "fps": 25,
      "bitrate": 4000000, "codec": "H265"
    },
    "infrared_stream": {
      "dds_topic": "thermal_video_frames", 
      "width": 640, "height": 512, "fps": 30,
      "bitrate": 1000000, "codec": "H265"
    }
  }
}
```

### 3. 30分钟测试脚本 (`run_30min_dual_video_test.sh`)
- 完整的30分钟录制测试
- 实时性能监控
- 自动文件验证
- 结果归档

## 运行测试

### 1. 基础单元测试
```bash
# 编译测试
mkdir build && cd build
cmake ..
make test_video_control

# 运行基础测试
./test_video_control

# 运行包含30分钟测试的完整测试
./test_video_control --30min
```

### 2. 独立30分钟测试
```bash
# 确保服务已编译
make video_control

# 运行30分钟测试脚本
chmod +x scripts/run_30min_dual_video_test.sh
./scripts/run_30min_dual_video_test.sh
```

### 3. Python客户端测试
```bash
# 运行Python测试客户端
python3 scripts/test_dual_video_client.py test

# 单独测试拍照
python3 scripts/test_dual_video_client.py photo

# 单独测试录像
python3 scripts/test_dual_video_client.py start
python3 scripts/test_dual_video_client.py stop
```

## 测试场景

### 1. 快速验证测试 (5分钟)
```bash
# 基础功能测试
./test_video_control

# 预期结果:
# ✓ 双路配置加载成功
# ✓ 双路DDS连接建立
# ✓ 双路拍照功能验证
# ✓ 双路录像功能验证
```

### 2. 30分钟完整录制测试
```bash
# 长时间稳定性测试
./scripts/run_30min_dual_video_test.sh

# 预期结果:
# ✓ 6个可见光视频段 (每段5分钟)
# ✓ 6个红外视频段 (每段5分钟)  
# ✓ 多张双路照片
# ✓ 总数据量约900MB
# ✓ 服务稳定运行30分钟
```

### 3. 压力测试
```bash
# 高频命令测试
python3 scripts/test_dual_video_client.py test --stress

# 预期结果:
# ✓ 高频拍照命令响应正常
# ✓ 录像过程中拍照不影响录制
# ✓ 系统资源使用稳定
```

## 测试数据分析

### 30分钟测试预期数据量

#### 可见光流 (1920x1080@25fps, 4Mbps)
```
每5分钟段:
- 帧数: 7,500帧
- 原始数据: ~2.9GB
- 编码后: ~150MB
- 6段总计: ~900MB
```

#### 红外流 (640x512@30fps, 1Mbps)  
```
每5分钟段:
- 帧数: 9,000帧
- 原始数据: ~1.4GB
- 编码后: ~37.5MB
- 6段总计: ~225MB
```

#### 总计
```
- 总视频文件: 12个 (6个可见光 + 6个红外)
- 总数据量: ~1.1GB
- 照片文件: 6张 (3个时间点 × 2路)
- 所需存储: 最少2GB (含安全余量)
```

## 性能要求

### 系统资源
- **CPU使用率**: <80%
- **内存使用**: <500MB
- **磁盘写入速率**: ~2MB/s
- **网络带宽**: 5Mbps (DDS输入)

### 存储要求
- **最小空间**: 2GB
- **推荐空间**: 4GB
- **写入速度**: >10MB/s
- **IOPS**: >100

## 故障排除

### 常见问题

1. **DDS连接失败**
```bash
# 检查DDS发布者是否运行
fastdds discovery -i 0

# 检查主题是否存在
fastdds topic list
```

2. **编码器初始化失败**
```bash
# 检查GStreamer插件
gst-inspect-1.0 mpph265enc
gst-inspect-1.0 mpph264enc

# 检查硬件编码器支持
ls /dev/mpp*
```

3. **存储空间不足**
```bash
# 检查可用空间
df -h /media/sdcard

# 清理旧文件
find /media/sdcard -name "*.mp4" -mtime +7 -delete
```

4. **网络端口冲突**
```bash
# 检查端口占用
netstat -ln | grep 14551

# 使用其他端口
./video_control --port 14552
```

### 日志分析

#### 服务日志位置
```
- 服务日志: /tmp/test_video_control.log
- 测试日志: /tmp/dual_video_test_30min.log
- 性能日志: /tmp/test_dual_video_30min/service.log
```

#### 关键日志信息
```
[INFO] Dual recording session started
[INFO] Created dual video segments
[INFO] Visible photo captured
[INFO] Infrared photo captured
[WARN] Segment creation behind schedule
[ERROR] Failed to create video segment
```

## 验收标准

### 功能验收
- [x] 双路DDS主题连接成功
- [x] 双路拍照功能正常
- [x] 双路录像功能正常
- [x] 5分钟自动分段工作
- [x] Mavlink命令响应正常

### 性能验收
- [x] 30分钟连续录制无中断
- [x] CPU使用率<80%
- [x] 内存使用<500MB
- [x] 所有视频段可独立播放
- [x] 照片质量符合要求

### 稳定性验收
- [x] 服务运行30分钟无崩溃
- [x] 网络中断后自动恢复
- [x] 存储空间不足时优雅处理
- [x] 错误日志记录完整

## 测试报告模板

```
=== 双路视频控制服务测试报告 ===

测试时间: YYYY-MM-DD HH:MM:SS
测试版本: v1.0
测试环境: [硬件配置]

功能测试结果:
✓ 双路配置加载: PASS
✓ 双路拍照功能: PASS  
✓ 双路录像功能: PASS
✓ 自动分段功能: PASS

30分钟测试结果:
✓ 可见光视频段: 6/6
✓ 红外视频段: 6/6
✓ 双路照片: 6/6
✓ 总数据量: XXX MB
✓ 服务稳定性: PASS

性能指标:
- 平均CPU使用: XX%
- 平均内存使用: XXX MB
- 磁盘写入速率: XX MB/s
- 响应时间: XX ms

测试结论: PASS/FAIL
备注: [问题说明]
```
