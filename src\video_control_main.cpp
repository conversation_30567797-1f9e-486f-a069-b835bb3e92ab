#include "video_control.h"
#include "config_loader.h"
#include <signal.h>
#include <getopt.h>
#include <iostream>
#include <cstring>

// 全局变量
std::unique_ptr<VideoControlService> g_control_service;
std::atomic<bool> g_stop{false};

// 信号处理函数
void signal_handler(int signal) {
    switch (signal) {
        case SIGINT:
        case SIGTERM:
            g_stop.store(true);
            break;
        case SIGUSR1:
        case SIGUSR2:
            if (g_control_service) {
                g_control_service->handle_signal(signal);
            }
            break;
        default:
            break;
    }
}

void print_usage(const char* program_name) {
    std::cout << "Usage: " << program_name << " [OPTIONS]\n"
              << "Options:\n"
              << "  -c, --config FILE     Configuration file (default: config/video_control.json)\n"
              << "  -p, --port PORT       UDP port for Mavlink (default: 14551)\n"
              << "  -i, --ip IP           UDP bind IP address (default: 0.0.0.0)\n"
              << "  -s, --sdcard PATH     SD card mount path (default: /media/sdcard)\n"
              << "  --photo-path PATH     Photo save path (default: /media/sdcard/photos)\n"
              << "  --video-path PATH     Video save path (default: /media/sdcard/videos)\n"
              << "  --topic TOPIC         DDS input topic (default: Video_Frames)\n"
              << "  --codec CODEC         Video codec (H264|H265, default: H265)\n"
              << "  --segment-duration N  Video segment duration in minutes (default: 5)\n"
              << "  --width WIDTH         Video width (default: 1280)\n"
              << "  --height HEIGHT       Video height (default: 720)\n"
              << "  --fps FPS             Video frame rate (default: 30)\n"
              << "  --bitrate BITRATE     Video bitrate in bps (default: 2000000)\n"
              << "  --status-interval N   Status report interval in seconds (default: 2)\n"
              << "  --debug               Enable debug logging\n"
              << "  --help                Show this help message\n"
              << "\n"
              << "Video Control Service Features:\n"
              << "  1. Receive Mavlink messages via UDP on specified port\n"
              << "  2. Monitor SD card status (insertion, capacity, usage)\n"
              << "  3. Capture photos from DDS video frames on Mavlink command\n"
              << "  4. Record video segments (5-minute chunks) on Mavlink command\n"
              << "  5. Report system status every 2 seconds\n"
              << "\n"
              << "Supported Mavlink Commands:\n"
              << "  MAV_CMD_TAKE_PHOTO (1)      - Capture a photo\n"
              << "  MAV_CMD_START_RECORDING (2) - Start video recording\n"
              << "  MAV_CMD_STOP_RECORDING (3)  - Stop video recording\n"
              << "  MAV_CMD_GET_STATUS (4)      - Get system status\n";
}

int main(int argc, char* argv[]) {
    // 默认配置
    VideoControlConfig config;
    
    // 配置文件路径
    std::string config_file = ConfigLoader::get_default_config_path("video_control");
    
    // 命令行参数解析
    static struct option long_options[] = {
        {"config", required_argument, 0, 'c'},
        {"port", required_argument, 0, 'p'},
        {"ip", required_argument, 0, 'i'},
        {"sdcard", required_argument, 0, 's'},
        {"photo-path", required_argument, 0, 1},
        {"video-path", required_argument, 0, 2},
        {"topic", required_argument, 0, 't'},
        {"codec", required_argument, 0, 3},
        {"segment-duration", required_argument, 0, 4},
        {"width", required_argument, 0, 'w'},
        {"height", required_argument, 0, 'h'},
        {"fps", required_argument, 0, 'f'},
        {"bitrate", required_argument, 0, 'b'},
        {"status-interval", required_argument, 0, 5},
        {"debug", no_argument, 0, 'd'},
        {"help", no_argument, 0, 6},
        {0, 0, 0, 0}
    };
    
    // 第一次解析：获取配置文件路径
    int c;
    optind = 1;
    while ((c = getopt_long(argc, argv, "c:p:i:s:t:w:h:f:b:d", long_options, nullptr)) != -1) {
        if (c == 'c') {
            config_file = optarg;
            break;
        }
    }
    
    // 加载配置文件
    if (!ConfigLoader::load_video_control_config(config_file, config)) {
        LOG_W("Failed to load config file: %s, using default settings", config_file.c_str());
    }
    
    // 第二次解析：处理所有命令行参数（覆盖配置文件设置）
    optind = 1;
    while ((c = getopt_long(argc, argv, "c:p:i:s:t:w:h:f:b:d", long_options, nullptr)) != -1) {
        switch (c) {
            case 'c':
                // 配置文件已处理
                break;
            case 'p':
                config.udp_port = atoi(optarg);
                break;
            case 'i':
                config.udp_bind_ip = optarg;
                break;
            case 's':
                config.sdcard_mount_path = optarg;
                break;
            case 1:
                config.photo_save_path = optarg;
                break;
            case 2:
                config.video_save_path = optarg;
                break;
            case 't':
                config.input_topic = optarg;
                break;
            case 3:
                config.video_codec = optarg;
                break;
            case 4:
                config.video_segment_duration_min = atoi(optarg);
                break;
            case 'w':
                config.video_width = atoi(optarg);
                break;
            case 'h':
                config.video_height = atoi(optarg);
                break;
            case 'f':
                config.video_fps = atoi(optarg);
                break;
            case 'b':
                config.video_bitrate = atoi(optarg);
                break;
            case 5:
                config.status_report_interval_sec = atoi(optarg);
                break;
            case 'd':
                config.enable_debug = true;
                config.log_level = "DEBUG";
                break;
            case 6:
                print_usage(argv[0]);
                return 0;
            default:
                print_usage(argv[0]);
                return 1;
        }
    }
    
    // 验证配置
    if (config.udp_port <= 0 || config.udp_port > 65535) {
        std::cerr << "Invalid UDP port: " << config.udp_port << std::endl;
        return 1;
    }
    
    if (config.video_segment_duration_min <= 0) {
        std::cerr << "Invalid segment duration: " << config.video_segment_duration_min << std::endl;
        return 1;
    }
    
    // 设置日志级别
    if (config.log_level == "DEBUG") {
        Logger::set_level(LOG_DEBUG);
    } else if (config.log_level == "INFO") {
        Logger::set_level(LOG_INFO);
    } else if (config.log_level == "WARN") {
        Logger::set_level(LOG_WARN);
    } else {
        Logger::set_level(LOG_ERROR);
    }
    
    // 设置信号处理
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);
    signal(SIGUSR1, signal_handler);
    signal(SIGUSR2, signal_handler);
    
    LOG_I("Starting Video Control Service...");
    LOG_I("UDP Mavlink: %s:%d", config.udp_bind_ip.c_str(), config.udp_port);
    LOG_I("SD Card: %s", config.sdcard_mount_path.c_str());
    LOG_I("Photo Path: %s", config.photo_save_path.c_str());
    LOG_I("Video Path: %s", config.video_save_path.c_str());
    LOG_I("DDS Topic: %s", config.input_topic.c_str());
    LOG_I("Video: %s %dx%d@%dfps, %d kbps, %d min segments",
          config.video_codec.c_str(), config.video_width, config.video_height,
          config.video_fps, config.video_bitrate / 1000, config.video_segment_duration_min);
    
    int ret = 0;
    try {
        // 创建并初始化服务
        g_control_service = std::make_unique<VideoControlService>();
        if (!g_control_service->init(config)) {
            LOG_E("Failed to initialize video control service");
            ret = 1;
            goto exit_;
        }
        
        // 启动服务
        g_control_service->start();
        
        // 主循环 - 定期输出统计信息
        while (!g_stop.load()) {
            std::this_thread::sleep_for(std::chrono::seconds(config.stats_interval_sec));
            if (g_control_service && !g_stop.load()) {
                VideoControlService::Stats stats;
                g_control_service->get_stats(stats);
                SDCardStatus sd_status = g_control_service->get_sdcard_status();
                
                LOG_I("Stats - Mavlink: %lu/%lu, Photos: %lu/%lu, Recording: %s, Segments: %lu",
                      stats.mavlink_messages_received, stats.mavlink_messages_processed,
                      stats.photos_taken, stats.photos_failed,
                      sd_status.is_recording ? "YES" : "NO", stats.video_segments_created);
                LOG_I("SD Card - Mounted: %s, Space: %lu/%lu MB (%.1f%% used)",
                      sd_status.is_mounted ? "YES" : "NO",
                      sd_status.used_capacity_mb, sd_status.total_capacity_mb,
                      sd_status.usage_percentage);
            }
        }
        
    } catch (const std::exception& e) {
        LOG_E("Exception in main: %s", e.what());
        ret = 1;
    }
    
exit_:
    LOG_I("Video Control Service stopped");
    if (g_control_service) {
        g_control_service->stop();
        g_control_service.reset();
    }
    return ret;
}
