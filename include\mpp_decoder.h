#ifndef MPP_DECODER_H
#define MPP_DECODER_H

#include "common.h"

// MPP 解码头文件
#include "rockchip/rk_mpi.h"
#include "rockchip/mpp_task.h"
#include "rockchip/mpp_log.h"
#include "rockchip/mpp_packet.h"
#include "rockchip/mpp_frame.h"
#include "rockchip/mpp_buffer.h"
#include "rockchip/mpp_meta.h"
#include <string.h>
#include <chrono>
#include <thread>

#define MPP_ALIGN(x, a)         (((x)+(a)-1)&~((a)-1))

// 支持的编码类型
typedef enum {
    MPP_DECODER_TYPE_MJPEG = 0,
    MPP_DECODER_TYPE_H264,
    MPP_DECODER_TYPE_H265,
    MPP_DECODER_TYPE_BUTT
} MPPDecoderType;

// 解码器状态
typedef enum {
    MPP_DECODER_STATE_UNINIT = 0,
    MPP_DECODER_STATE_CONFIGURED,
    MPP_DECODER_STATE_ERROR
} MPPDecoderState;

// 内部数据结构
typedef struct {
    MppCtx          ctx;
    MppApi          *mpi;
    MppDecCfg       cfg;

    /* input and output */
    MppPacket       packet;
    MppFrame        frame;

    /* frame buffer management */
    MppBufferGroup  frm_grp;
    MppBuffer       frm_buf;

    /* packet buffer management */
    MppBufferGroup  pkt_grp;
    MppBuffer       pkt_buf;

    /* decoder configuration */
    MPPDecoderType  decoder_type;
    MppCodingType   coding_type;
    MppFrameFormat  format;
    RK_U32          width;
    RK_U32          height;

    /* memory management */
    size_t          max_usage;
    RK_U32          frame_count;

    /* performance tracking */
    RK_S64          first_pkt;
    RK_S64          first_frm;

} MpiDecLoopData;

// MPP 解码器接口 - 每个实例只支持一种编码格式
class MPPDecoder {
private:
    const MPPDecoderType decoder_type_;  // 编码格式在构造时确定，不可修改
    const MppCodingType coding_type_;    // 对应的MPP编码类型
    MPPDecoderState state_ = MPP_DECODER_STATE_UNINIT;
    MpiDecLoopData loop_data_;
    bool need_split_ = false;  // 每个package都是完整frame，禁用split

public:
    // 构造函数 - 指定编码格式
    explicit MPPDecoder(MPPDecoderType decoder_type);
    explicit MPPDecoder(int32_t v4l2_format);  // 兼容V4L2格式的构造函数
    ~MPPDecoder() { cleanup(); }

    // 禁用拷贝构造和赋值操作
    MPPDecoder(const MPPDecoder&) = delete;
    MPPDecoder& operator=(const MPPDecoder&) = delete;
    MPPDecoder(MPPDecoder&&) = delete;
    MPPDecoder& operator=(MPPDecoder&&) = delete;

    // 初始化解码器 - 只需要指定分辨率
    bool init(int width, int height);

    // 解码接口 - 统一的解码方法
    bool decode_frame(const Frame& src, Frame& dst);

    // 状态管理
    void cleanup();
    bool is_initialized() const { return state_ == MPP_DECODER_STATE_CONFIGURED; }
    MPPDecoderState get_state() const { return state_; }

    // 获取解码器信息
    MPPDecoderType get_decoder_type() const { return decoder_type_; }
    MppCodingType get_coding_type() const { return coding_type_; }
    const char* get_decoder_type_name() const;

    // 性能和内存信息
    size_t get_max_memory_usage() const { return loop_data_.max_usage; }
    RK_U32 get_frame_count() const { return loop_data_.frame_count; }

private:
    // 内部初始化和配置
    bool init_mpp_context();
    bool configure_decoder();
    bool setup_frame_buffer_group(RK_U32 buf_size, RK_U32 count);
    bool setup_mjpeg_frame_buffers();

    // 解码核心逻辑
    bool decode_packet_internal(const void* data, size_t size, Frame& dst);
    bool get_decoded_frame(MppFrame mpp_frame, Frame& dst);
    bool handle_info_change(MppFrame frame);

    // 内存管理
    void cleanup_internal();
    void update_memory_usage();

    // 工具函数
    static MppCodingType get_mpp_coding_type(MPPDecoderType decoder_type);
    static MPPDecoderType get_decoder_type_from_v4l2(int32_t v4l2_format);
    static const char* get_decoder_type_name(MPPDecoderType type);
};

// MPPDecoder实现
inline MPPDecoder::MPPDecoder(MPPDecoderType decoder_type)
    : decoder_type_(decoder_type), coding_type_(get_mpp_coding_type(decoder_type)) {
    if (coding_type_ == MPP_VIDEO_CodingUnused) {
        LOG_E("Unsupported decoder type: %d", decoder_type);
        state_ = MPP_DECODER_STATE_ERROR;
    } else {
        state_ = MPP_DECODER_STATE_UNINIT;
        LOG_I("MPP decoder created for %s", get_decoder_type_name(decoder_type_));
    }

    memset(&loop_data_, 0, sizeof(loop_data_));
    loop_data_.decoder_type = decoder_type_;
    loop_data_.coding_type = coding_type_;
    need_split_ = false;  // 禁用split，每个package都是完整frame
}

// 兼容V4L2格式的构造函数
inline MPPDecoder::MPPDecoder(int32_t v4l2_format)
    : MPPDecoder(get_decoder_type_from_v4l2(v4l2_format)) {
    if (decoder_type_ == MPP_DECODER_TYPE_BUTT) {
        LOG_E("Unsupported V4L2 format: 0x%08x", v4l2_format);
        state_ = MPP_DECODER_STATE_ERROR;
    }
}

// 初始化解码器 - 只需要指定分辨率，编码格式已在构造时确定
inline bool MPPDecoder::init(int width, int height) {
    if (state_ != MPP_DECODER_STATE_UNINIT) {
        LOG_E("Decoder is in error state, cannot initialize");
        return false;
    }

    if (width <= 0 || height <= 0) {
        LOG_E("Invalid resolution: %dx%d", width, height);
        return false;
    }

    loop_data_.width = width;
    loop_data_.height = height;
    loop_data_.format = MPP_FMT_YUV420SP;
    loop_data_.frame_count = 0;

    if (!init_mpp_context()) {
        LOG_E("Failed to initialize MPP context");
        cleanup_internal();
        return false;
    }

    if (!configure_decoder()) {
        LOG_E("Failed to configure decoder");
        cleanup_internal();
        return false;
    }

    state_ = MPP_DECODER_STATE_CONFIGURED;
    LOG_I("MPP %s decoder initialized: %dx%d",
          get_decoder_type_name(), width, height);

    return true;
}

// 统一的解码接口 - 根据构造时确定的编码格式进行解码
inline bool MPPDecoder::decode_frame(const Frame& src, Frame& dst) {
    if (state_ != MPP_DECODER_STATE_CONFIGURED) {
        LOG_E("Decoder not properly initialized");
        return false;
    }

    if (src.data.empty()) {
        LOG_E("Empty input frame data");
        return false;
    }

    // 验证输入帧格式是否与解码器类型匹配
    MPPDecoderType expected_type = get_decoder_type_from_v4l2(src.format);
    if (expected_type != MPP_DECODER_TYPE_BUTT && expected_type != decoder_type_) {
        LOG_W("Input frame format (0x%08x) doesn't match decoder type (%s)",
                src.format, get_decoder_type_name());
    }

    // 调用统一的解码实现
    return decode_packet_internal(src.data.data(), src.data.size(), dst);
}

// 清理资源
inline void MPPDecoder::cleanup() {
    cleanup_internal();
    state_ = MPP_DECODER_STATE_UNINIT;
}

// 内部清理实现 - packet 和 frame 在 init 中初始化，需要在这里清理
inline void MPPDecoder::cleanup_internal() {
    // 清理在 init 中初始化的 packet 和 frame
    if (loop_data_.packet) {
        mpp_packet_deinit(&loop_data_.packet);
        loop_data_.packet = NULL;
    }

    if (loop_data_.frame) {
        mpp_frame_deinit(&loop_data_.frame);
        loop_data_.frame = NULL;
    }

    if (loop_data_.mpi && loop_data_.ctx) {
        loop_data_.mpi->reset(loop_data_.ctx);
    }

    if (loop_data_.ctx) {
        mpp_destroy(loop_data_.ctx);
        loop_data_.ctx = NULL;
        loop_data_.mpi = NULL;
    }

    // 清理frame buffer
    if (loop_data_.frm_buf) {
        mpp_buffer_put(loop_data_.frm_buf);
        loop_data_.frm_buf = NULL;
    }

    if (loop_data_.frm_grp) {
        mpp_buffer_group_put(loop_data_.frm_grp);
        loop_data_.frm_grp = NULL;
    }

    // 清理packet buffer
    if (loop_data_.pkt_buf) {
        mpp_buffer_put(loop_data_.pkt_buf);
        loop_data_.pkt_buf = NULL;
    }

    if (loop_data_.pkt_grp) {
        mpp_buffer_group_put(loop_data_.pkt_grp);
        loop_data_.pkt_grp = NULL;
    }

    if (loop_data_.cfg) {
        mpp_dec_cfg_deinit(loop_data_.cfg);
        loop_data_.cfg = NULL;
    }

    LOG_D("MPP decoder cleanup completed");
}

// 工具函数：获取MPP编码类型
inline MppCodingType MPPDecoder::get_mpp_coding_type(MPPDecoderType decoder_type) {
    switch (decoder_type) {
        case MPP_DECODER_TYPE_MJPEG:
            return MPP_VIDEO_CodingMJPEG;
        case MPP_DECODER_TYPE_H264:
            return MPP_VIDEO_CodingAVC;
        case MPP_DECODER_TYPE_H265:
            return MPP_VIDEO_CodingHEVC;
        default:
            return MPP_VIDEO_CodingUnused;
    }
}

// 工具函数：从V4L2格式获取解码器类型
inline MPPDecoderType MPPDecoder::get_decoder_type_from_v4l2(int32_t v4l2_format) {
    switch (v4l2_format) {
        case V4L2_PIX_FMT_MJPEG:
            return MPP_DECODER_TYPE_MJPEG;
        case V4L2_PIX_FMT_H264:
            return MPP_DECODER_TYPE_H264;
        case V4L2_PIX_FMT_H265:
        case V4L2_PIX_FMT_HEVC:
            return MPP_DECODER_TYPE_H265;
        default:
            return MPP_DECODER_TYPE_BUTT;
    }
}

// 获取当前解码器类型名称
inline const char* MPPDecoder::get_decoder_type_name() const {
    return get_decoder_type_name(decoder_type_);
}

// 工具函数：获取解码器类型名称
inline const char* MPPDecoder::get_decoder_type_name(MPPDecoderType type) {
    switch (type) {
        case MPP_DECODER_TYPE_MJPEG:
            return "MJPEG";
        case MPP_DECODER_TYPE_H264:
            return "H264";
        case MPP_DECODER_TYPE_H265:
            return "H265";
        default:
            return "Unknown";
    }
}

// 更新内存使用统计
inline void MPPDecoder::update_memory_usage() {
    if (loop_data_.frm_grp) {
        size_t usage = mpp_buffer_group_usage(loop_data_.frm_grp);
        if (usage > loop_data_.max_usage) {
            loop_data_.max_usage = usage;
        }
    }
}

// MPP上下文初始化 - 基于 mpi_dec_test.c
inline bool MPPDecoder::init_mpp_context() {
    MPP_RET ret = MPP_OK;

    // 创建MPP上下文
    ret = mpp_create(&loop_data_.ctx, &loop_data_.mpi);
    if (ret != MPP_OK) {
        LOG_E("mpp_create failed: %d", ret);
        return false;
    }

    // 初始化解码器，使用构造时确定的编码类型
    ret = mpp_init(loop_data_.ctx, MPP_CTX_DEC, coding_type_);
    if (ret != MPP_OK) {
        LOG_E("mpp_init failed: %d", ret);
        return false;
    }

    ret = mpp_frame_init(&loop_data_.frame);
    if (ret != MPP_OK) {
        LOG_E("mpp_frame_init failed: %d", ret);
        return false;
    }

    // 对于 MJPEG，需要在初始化时设置 frame buffer group 和 frame buf
    if (decoder_type_ == MPP_DECODER_TYPE_MJPEG) {
        // 初始化packet buffer group
        ret = mpp_buffer_group_get_internal(&loop_data_.pkt_grp, MPP_BUFFER_TYPE_ION);
        if (ret != MPP_OK) {
            LOG_E("Failed to get packet buffer group: %d", ret);
            return false;
        }
        // 初始化frame buffer group
        if (!setup_mjpeg_frame_buffers()) {
            LOG_E("Failed to setup MJPEG buffers");
            return false;
        }
    } else {
        ret = mpp_packet_init(&loop_data_.packet, NULL, 0);
        if (ret != MPP_OK) {
            LOG_E("mpp_packet_init failed: %d", ret);
            return false;
        }
    }

    LOG_D("MPP context initialized for %s", get_decoder_type_name());
    return true;
}

// 配置解码器
inline bool MPPDecoder::configure_decoder() {
    MPP_RET ret = MPP_OK;

    // 初始化配置
    ret = mpp_dec_cfg_init(&loop_data_.cfg);
    if (ret != MPP_OK) {
        LOG_E("mpp_dec_cfg_init failed: %d", ret);
        return false;
    }

    // 获取默认配置
    ret = loop_data_.mpi->control(loop_data_.ctx, MPP_DEC_GET_CFG, loop_data_.cfg);
    if (ret != MPP_OK) {
        LOG_E("Failed to get decoder cfg: %d", ret);
        return false;
    }

    // 设置 split_parse 为 0，因为每个 package 都是完整 frame
    ret = mpp_dec_cfg_set_u32(loop_data_.cfg, "base:split_parse", need_split_ ? 1 : 0);
    if (ret != MPP_OK) {
        LOG_E("Failed to set split_parse: %d", ret);
        return false;
    }

    // 应用配置
    ret = loop_data_.mpi->control(loop_data_.ctx, MPP_DEC_SET_CFG, loop_data_.cfg);
    if (ret != MPP_OK) {
        LOG_E("Failed to set cfg: %d", ret);
        return false;
    }

    LOG_D("Decoder configured with split_parse=%d", need_split_ ? 1 : 0);

    // MJPEG 需要设置输出格式
    if (decoder_type_ == MPP_DECODER_TYPE_MJPEG) {
        ret = loop_data_.mpi->control(loop_data_.ctx, MPP_DEC_SET_OUTPUT_FORMAT, &loop_data_.format);
        if (ret != MPP_OK) {
            LOG_E("Failed to set output format 0x%x\n", loop_data_.format);
            return false;
        }
    }

    return true;
}

// 核心解码函数 - 使用 decode_put_packet 和 decode_get_frame
inline bool MPPDecoder::decode_packet_internal(const void* data, size_t size, Frame& dst) {
    MPP_RET ret = MPP_OK;
    MppFrame frame_ret = NULL;
    bool result = false;
    RK_S32 times = 30;  // 重试次数

    if (!loop_data_.first_pkt) {
        loop_data_.first_pkt = std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::system_clock::now().time_since_epoch()).count();
    }

    // MJPEG 模式下，需要设置输出帧和packet buffer
    if (loop_data_.decoder_type == MPP_DECODER_TYPE_MJPEG) {
        // 获取packet buffer
        ret = mpp_buffer_get(loop_data_.pkt_grp, &loop_data_.pkt_buf, size);
        if (ret != MPP_OK) {
            LOG_E("Failed to get packet buffer: %d", ret);
            return false;
        }
        /* FIXME: performance bad */
        memcpy (mpp_buffer_get_ptr (loop_data_.pkt_buf), data, size);
        mpp_packet_init_with_buffer (&loop_data_.packet, loop_data_.pkt_buf);
        if (loop_data_.packet == NULL) {
            LOG_E("Failed to init packet with buffer");
            mpp_buffer_put(loop_data_.pkt_buf);
            return false;
        }
        mpp_packet_set_size (loop_data_.packet, size);
        mpp_packet_set_length (loop_data_.packet, size);

        MppMeta meta = mpp_packet_get_meta(loop_data_.packet);
        if (meta) {
            mpp_meta_set_frame(meta, KEY_OUTPUT_FRAME, loop_data_.frame);
        }
    } else {
        // 设置 packet 数据
        mpp_packet_set_data(loop_data_.packet, (void*)data);
        mpp_packet_set_size(loop_data_.packet, size);
        mpp_packet_set_pos(loop_data_.packet, (void*)data);
        mpp_packet_set_length(loop_data_.packet, size);
    }

    // 使用 decode_put_packet 发送数据包
    ret = loop_data_.mpi->decode_put_packet(loop_data_.ctx, loop_data_.packet);
    if (ret != MPP_OK) {
        LOG_E("decode_put_packet failed: %d", ret);
        goto release_packet_buffer;
    }

    do {
        ret = loop_data_.mpi->decode_get_frame(loop_data_.ctx, &frame_ret);
        if (ret == MPP_ERR_TIMEOUT) {
            if (times > 0) {
                times--;
                // 等待 1ms 后重试
                LOG_D("decode_get_frame timeout, retrying...");
                std::this_thread::sleep_for(std::chrono::milliseconds(1));
                continue;
            }
            LOG_W("decode_get_frame timeout after too many retries");
            goto release_packet_buffer;
        }
        if (ret != MPP_OK) {
            LOG_E("decode_get_frame failed: %d", ret);
            goto release_packet_buffer;
        }
        break;
    } while (times > 0);

    if (!frame_ret) {
        LOG_W("No frame output from decoder");
        goto release_packet_buffer;
    }

    if (loop_data_.decoder_type == MPP_DECODER_TYPE_MJPEG) {
        // MJPEG 模式下，解码后的帧直接使用预分配的缓冲区
        if (frame_ret != loop_data_.frame) {
            LOG_E("MJPEG mode: unexpected frame returned %p --> %p", loop_data_.frame, frame_ret);
            mpp_frame_deinit(&frame_ret);
            goto release_frame_buffer;
        }
    } else {
        // 检查是否有信息变化
        if (mpp_frame_get_info_change(frame_ret)) {
            handle_info_change(frame_ret);
            goto release_frame_buffer;
        }

        // 检查错误信息
        RK_U32 err_info = mpp_frame_get_errinfo(frame_ret);
        RK_U32 discard = mpp_frame_get_discard(frame_ret);

        if (err_info || discard) {
            LOG_W("Frame decode warning - err: 0x%x, discard: 0x%x", err_info, discard);
            if (err_info) {
                goto release_frame_buffer;
            }
        }
    }

    // 获取解码后的帧数据
    result = get_decoded_frame(frame_ret, dst);
    if (result) {
        loop_data_.frame_count++;
        update_memory_usage();

        if (!loop_data_.first_frm) {
            loop_data_.first_frm = std::chrono::duration_cast<std::chrono::milliseconds>(std::chrono::system_clock::now().time_since_epoch()).count();
        }

        LOG_D("Frame %d decoded successfully", loop_data_.frame_count);
    }

release_frame_buffer:
    if (loop_data_.decoder_type != MPP_DECODER_TYPE_MJPEG) {
        // 非 MJPEG 模式下，需要释放 frame
        mpp_frame_deinit(&frame_ret);
    }
release_packet_buffer:
    // 释放packet buffer，非 MJPEG 模式下，需要释放 packet
    if (loop_data_.decoder_type == MPP_DECODER_TYPE_MJPEG) {
        mpp_packet_deinit(&loop_data_.packet);
        mpp_buffer_put(loop_data_.pkt_buf);
        loop_data_.pkt_buf = NULL;
    }

    return result;
}

// 处理信息变化 非 MJPEG 模式（H264/H265）：只在信息变化时设置外部缓冲区组
inline bool MPPDecoder::handle_info_change(MppFrame frame) {
    MPP_RET ret = MPP_OK;

    RK_U32 width = mpp_frame_get_width(frame);
    RK_U32 height = mpp_frame_get_height(frame);
    RK_U32 hor_stride = mpp_frame_get_hor_stride(frame);
    RK_U32 ver_stride = mpp_frame_get_ver_stride(frame);
    RK_U32 buf_size = mpp_frame_get_buf_size(frame);

    LOG_I("Info change detected: %dx%d, stride: %dx%d, buf_size: %d",
          width, height, hor_stride, ver_stride, buf_size);

    if (!setup_frame_buffer_group(buf_size, 20)) {
        LOG_E("Failed to setup buffer group for non-MJPEG");
        return false;
    }
    LOG_D("Non-MJPEG mode: external buffer group setup completed");
    // 设置缓冲区组到解码器
    ret = loop_data_.mpi->control(loop_data_.ctx, MPP_DEC_SET_EXT_BUF_GROUP, loop_data_.frm_grp);
    if (ret != MPP_OK) {
        LOG_E("Failed to set buffer group: %d", ret);
        return false;
    }
    // 通知解码器信息变化已处理
    ret = loop_data_.mpi->control(loop_data_.ctx, MPP_DEC_SET_INFO_CHANGE_READY, NULL);
    if (ret != MPP_OK) {
        LOG_E("Failed to set info change ready: %d", ret);
        return false;
    }

    LOG_D("Info change handled successfully");
    return true;
}

// 获取解码后的帧数据
inline bool MPPDecoder::get_decoded_frame(MppFrame mpp_frame, Frame& dst) {
    if (!mpp_frame) {
        LOG_E("MPP frame is null");
        return false;
    }

    MppBuffer buffer = mpp_frame_get_buffer(mpp_frame);
    if (!buffer) {
        LOG_E("MPP frame buffer is null");
        return false;
    }

    // 获取帧信息
    dst.width = mpp_frame_get_width(mpp_frame);
    dst.height = mpp_frame_get_height(mpp_frame);
    dst.format = V4L2_PIX_FMT_NV12;  // MPP输出MPP_FMT_YUV420SP(NV12)格式
    dst.timestamp = std::chrono::duration_cast<std::chrono::milliseconds>(
        std::chrono::steady_clock::now().time_since_epoch()).count();
    dst.frame_id = loop_data_.frame_count;
    dst.is_keyframe = true;  // 解码后的帧标记为关键帧
    dst.valid = true;

    // 获取实际的解码数据
    void* ptr = mpp_buffer_get_ptr(buffer);
    size_t size = mpp_buffer_get_size(buffer);

    if (!ptr || size == 0) {
        LOG_E("Invalid buffer data: ptr=%p, size=%zu", ptr, size);
        return false;
    }

    // 复制数据到目标帧
    dst.data.resize(size);
    memcpy(dst.data.data(), ptr, size);

    // 获取额外的帧信息
    if (mpp_frame_has_meta(mpp_frame)) {
        MppMeta meta = mpp_frame_get_meta(mpp_frame);
        RK_S32 temporal_id = 0;
        if (mpp_meta_get_s32(meta, KEY_TEMPORAL_ID, &temporal_id) == MPP_OK) {
            LOG_D("Frame temporal_id: %d", temporal_id);
        }
    }

    LOG_D("Decoded frame: %dx%d, format=0x%08x, size=%zu",
          dst.width, dst.height, dst.format, dst.data.size());

    return true;
}

// 设置frame缓冲区组
inline bool MPPDecoder::setup_frame_buffer_group(RK_U32 buf_size, RK_U32 count) {
    MPP_RET ret = MPP_OK;

    // 清理旧的缓冲区组
    if (loop_data_.frm_grp) {
        mpp_buffer_group_put(loop_data_.frm_grp);
        loop_data_.frm_grp = NULL;
    }

    // 创建新的缓冲区组，使用合理的缓冲区数量
    ret = mpp_buffer_group_get_internal(&loop_data_.frm_grp, MPP_BUFFER_TYPE_ION);
    if (ret != MPP_OK) {
        LOG_E("Failed to get buffer group: %d", ret);
        return false;
    }

    /* Use limit config to limit buffer count and buffer size */
    ret = mpp_buffer_group_limit_config(loop_data_.frm_grp, buf_size, count);
    if (ret != MPP_OK) {
        LOG_E("limit buffer group failed ret %d\n", ret);
        return false;
    }

    LOG_D("Buffer group setup with buf_size: %d, count: %d", buf_size, count);
    return true;
}

// MJPEG frame缓冲区设置 - 在初始化时设置 frame buffer group 和 frame buf
inline bool MPPDecoder::setup_mjpeg_frame_buffers() {
    MPP_RET ret = MPP_OK;
    // 为 MJPEG 分配 frame buffer
    // MJPEG 可能有 YUV420 和 YUV422，缓冲区应该足够大
    // YUV420 缓冲区是 w*h 的 3/2 倍
    // YUV422 缓冲区是 w*h 的 2 倍
    // 所以创建 w*h*4 的缓冲区以确保足够
    RK_U32 hor_stride = MPP_ALIGN(loop_data_.width, 16);
    RK_U32 ver_stride = MPP_ALIGN(loop_data_.height, 16);
    RK_U32 buf_size = hor_stride * ver_stride * 4;

    // 为 MJPEG 创建 frame buffer group
    if (!setup_frame_buffer_group(buf_size, 1)) {
        LOG_E("Failed to setup frame buffer group for MJPEG: %d", ret);
        return false;
    }

    ret = mpp_buffer_get(loop_data_.frm_grp, &loop_data_.frm_buf, buf_size);
    if (ret != MPP_OK) {
        LOG_E("Failed to get frame buffer for MJPEG: %d", ret);
        return false;
    }

    // 将 buffer 设置到 frame
    mpp_frame_set_buffer(loop_data_.frame, loop_data_.frm_buf);

    LOG_D("MJPEG buffers setup: %dx%d, stride: %dx%d, buf_size: %d",
          loop_data_.width, loop_data_.height, hor_stride, ver_stride, buf_size);

    return true;
}

#endif // MPP_DECODER_H
