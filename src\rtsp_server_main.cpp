#include "rtsp_server.h"
#include <signal.h>
#include <getopt.h>
#include <iostream>
#include <fstream>
#include <chrono>
#include <thread>
#ifdef HAVE_JSONCPP
#include <json/json.h>
#endif

// 全局变量用于信号处理
static std::unique_ptr<RTSPServerService> g_rtsp_server;
static std::atomic<bool> g_shutdown_requested{false};

void signal_handler(int signal) {
    LOG_I("Received signal %d, shutting down...", signal);
    g_shutdown_requested.store(true);
    
    if (g_rtsp_server) {
        g_rtsp_server->stop();
    }
}

void print_usage(const char* program_name) {
    std::cout << "Usage: " << program_name << " [OPTIONS]\n"
              << "\nOptions:\n"
              << "  -c, --config FILE     Configuration file (JSON format)\n"
              << "  -t, --topic TOPIC     DDS topic name (default: Video_Frames)\n"
              << "  -a, --address ADDR    Server bind address (default: 0.0.0.0)\n"
              << "  -p, --port PORT       Server port (default: 8554)\n"
              << "  -m, --mount PATH      Mount point (default: /stream)\n"

              << "  -f, --fps FPS         Output framerate (default: 30)\n"
              << "  -b, --bitrate RATE    Output bitrate in bps (default: 2000000)\n"
              << "  -g, --gop-size SIZE   GOP size (default: 30)\n"
              << "  --hw-h264             Use H264 hardware encoder (default: true)\n"
              << "  --hw-h265             Use H265 hardware encoder (default: false)\n"
              << "  --sw-encoder          Force software encoder\n"
              << "  --max-clients NUM     Maximum clients (default: 10)\n"
              << "  --buffer-size SIZE    DDS buffer size (default: 5)\n"
              << "  --stats-interval SEC  Statistics print interval (default: 10)\n"
              << "  --gst-debug LEVEL     GStreamer debug level 0-6 (default: 2)\n"
              << "  --help                Show this help message\n"
              << "\nExamples:\n"
              << "  " << program_name << " --topic Video_Frames --port 8554\n"
              << "  " << program_name << " --config rtsp_server.json\n"
              << "  " << program_name << " --topic Cloud_Frames --mount /cloud --bitrate 5000000\n"
              << "\nRTSP URL format: rtsp://server_ip:port/mount_point\n"
              << "Example: rtsp://*************:8554/stream\n";
}

bool load_config_from_json(const std::string& config_file, RTSPServerConfig& config) {
#ifdef HAVE_JSONCPP
    std::ifstream file(config_file);
    if (!file.is_open()) {
        LOG_E("Failed to open config file: %s", config_file.c_str());
        return false;
    }

    Json::Value root;
    Json::CharReaderBuilder builder;
    std::string errors;

    if (!Json::parseFromStream(builder, file, &root, &errors)) {
        LOG_E("Failed to parse JSON config: %s", errors.c_str());
        return false;
    }
    
    // 解析配置
    if (root.isMember("dds_topic")) {
        config.dds_topic = root["dds_topic"].asString();
    }
    if (root.isMember("server_address")) {
        config.server_address = root["server_address"].asString();
    }
    if (root.isMember("server_port")) {
        config.server_port = root["server_port"].asInt();
    }
    if (root.isMember("mount_point")) {
        config.mount_point = root["mount_point"].asString();
    }

    if (root.isMember("output_fps")) {
        config.output_fps = root["output_fps"].asInt();
    }
    if (root.isMember("output_bitrate")) {
        config.output_bitrate = root["output_bitrate"].asInt();
    }
    if (root.isMember("gop_size")) {
        config.gop_size = root["gop_size"].asInt();
    }
    if (root.isMember("use_hw_encoder_h264")) {
        config.use_hw_encoder_h264 = root["use_hw_encoder_h264"].asBool();
    }
    if (root.isMember("use_hw_encoder_h265")) {
        config.use_hw_encoder_h265 = root["use_hw_encoder_h265"].asBool();
    }
    if (root.isMember("max_clients")) {
        config.max_clients = root["max_clients"].asInt();
    }
    if (root.isMember("buffer_size")) {
        config.buffer_size = root["buffer_size"].asInt();
    }
    if (root.isMember("adaptive_bitrate")) {
        config.adaptive_bitrate = root["adaptive_bitrate"].asBool();
    }
    if (root.isMember("min_bitrate")) {
        config.min_bitrate = root["min_bitrate"].asInt();
    }
    if (root.isMember("max_bitrate")) {
        config.max_bitrate = root["max_bitrate"].asInt();
    }
    if (root.isMember("gst_debug_level")) {
        config.gst_debug_level = root["gst_debug_level"].asInt();
        if (config.gst_debug_level < 0) config.gst_debug_level = 0;
        if (config.gst_debug_level > 6) config.gst_debug_level = 6;
    }

    LOG_I("Loaded configuration from %s", config_file.c_str());
    return true;
#else
    LOG_E("JsonCpp support not compiled in, cannot load JSON config file");
    return false;
#endif
}

void print_config(const RTSPServerConfig& config) {
    LOG_I("=== RTSP Server Configuration ===");
    LOG_I("DDS Topic: %s", config.dds_topic.c_str());
    LOG_I("Server: %s:%d%s", config.server_address.c_str(), 
          config.server_port, config.mount_point.c_str());
    LOG_I("Output: Original size@%dfps, %s, %d bps",
          config.output_fps, config.output_codec.c_str(), config.output_bitrate);
    LOG_I("GOP Size: %d", config.gop_size);
    LOG_I("H264 Hardware Encoder: %s", config.use_hw_encoder_h264 ? "yes" : "no");
    LOG_I("H265 Hardware Encoder: %s", config.use_hw_encoder_h265 ? "yes" : "no");
    LOG_I("Max Clients: %d", config.max_clients);
    LOG_I("Buffer Size: %d", config.buffer_size);
    LOG_I("Zero Copy: %s", config.zero_copy_mode ? "yes" : "no");
    LOG_I("GStreamer Debug Level: %d", config.gst_debug_level);
    if (config.adaptive_bitrate) {
        LOG_I("Adaptive Bitrate: %d - %d bps", config.min_bitrate, config.max_bitrate);
    }
}

int main(int argc, char* argv[]) {
    // 设置日志级别
    Logger::set_level(LEVEL_INFO);
    
    // 默认配置
    RTSPServerConfig config;
    std::string config_file;
    int stats_interval = 10; // 统计信息打印间隔(秒)
    
    // 第一次解析命令行参数，只获取配置文件路径
    static struct option long_options[] = {
        {"config", required_argument, 0, 'c'},
        {"topic", required_argument, 0, 't'},
        {"address", required_argument, 0, 'a'},
        {"port", required_argument, 0, 'p'},
        {"mount", required_argument, 0, 'm'},

        {"fps", required_argument, 0, 'f'},
        {"bitrate", required_argument, 0, 'b'},
        {"gop-size", required_argument, 0, 'g'},
        {"hw-h264", no_argument, 0, 1},
        {"hw-h265", no_argument, 0, 2},
        {"sw-encoder", no_argument, 0, 3},
        {"max-clients", required_argument, 0, 4},
        {"buffer-size", required_argument, 0, 5},
        {"stats-interval", required_argument, 0, 6},
        {"help", no_argument, 0, 7},
        {"gst-debug", required_argument, 0, 8},
        {0, 0, 0, 0}
    };
    
    int option_index = 0;
    int c;

    // 第一次解析：只获取配置文件路径
    optind = 1; // 重置 getopt
    while ((c = getopt_long(argc, argv, "c:t:a:p:m:f:b:g:",
                           long_options, &option_index)) != -1) {
        if (c == 'c') {
            config_file = optarg;
            break;
        }
    }

    // 加载配置文件（在解析其他命令行参数之前）
    if (!config_file.empty()) {
        if (!load_config_from_json(config_file, config)) {
            LOG_E("Failed to load configuration file");
            return 1;
        }
    }

    // 第二次解析：处理所有命令行参数（覆盖配置文件设置）
    optind = 1; // 重置 getopt
    option_index = 0;
    while ((c = getopt_long(argc, argv, "c:t:a:p:m:f:b:g:",
                           long_options, &option_index)) != -1) {
        switch (c) {
            case 'c':
                // 配置文件路径已经处理过了
                break;
            case 't':
                config.dds_topic = optarg;
                break;
            case 'a':
                config.server_address = optarg;
                break;
            case 'p':
                config.server_port = std::atoi(optarg);
                break;
            case 'm':
                config.mount_point = optarg;
                break;
            case 'f':
                config.output_fps = std::atoi(optarg);
                break;
            case 'b':
                config.output_bitrate = std::atoi(optarg);
                break;
            case 'g':
                config.gop_size = std::atoi(optarg);
                break;
            case 1: // --hw-h264
                config.use_hw_encoder_h264 = true;
                config.use_hw_encoder_h265 = false;  // 互斥选择
                break;
            case 2: // --hw-h265
                config.use_hw_encoder_h264 = false;  // 互斥选择
                config.use_hw_encoder_h265 = true;
                break;
            case 3: // --sw-encoder
                config.use_hw_encoder_h264 = false;
                config.use_hw_encoder_h265 = false;
                break;
            case 4: // --max-clients
                config.max_clients = std::atoi(optarg);
                break;
            case 5: // --buffer-size
                config.buffer_size = std::atoi(optarg);
                break;
            case 6: // --stats-interval
                stats_interval = std::atoi(optarg);
                break;
            case 7: // --help
                print_usage(argv[0]);
                return 0;
            case 8: // --gst-debug
                config.gst_debug_level = std::atoi(optarg);
                if (config.gst_debug_level < 0) config.gst_debug_level = 0;
                if (config.gst_debug_level > 6) config.gst_debug_level = 6;
                break;
            case '?':
                print_usage(argv[0]);
                return 1;
            default:
                break;
        }
    }

    // 验证配置 (移除尺寸验证，使用原始尺寸)
    
    if (config.output_fps <= 0 || config.output_fps > 120) {
        LOG_E("Invalid output framerate: %d", config.output_fps);
        return 1;
    }
    
    if (config.server_port <= 0 || config.server_port > 65535) {
        LOG_E("Invalid server port: %d", config.server_port);
        return 1;
    }
    
    // 打印配置
    print_config(config);
    
    // 设置信号处理
    signal(SIGINT, signal_handler);
    signal(SIGTERM, signal_handler);
    
    // 创建并初始化RTSP服务器
    g_rtsp_server = std::make_unique<RTSPServerService>();
    if (!g_rtsp_server->init(config)) {
        LOG_E("Failed to initialize RTSP server");
        return 1;
    }
    
    // 启动服务器
    if (!g_rtsp_server->start()) {
        LOG_E("Failed to start RTSP server");
        return 1;
    }
    
    LOG_I("RTSP server is running. Access stream at: rtsp://%s:%d%s", 
          config.server_address.c_str(), config.server_port, config.mount_point.c_str());
    LOG_I("Press Ctrl+C to stop the server");
    
    // 主循环 - 定期打印统计信息
    auto last_stats_time = std::chrono::steady_clock::now();
    
    while (!g_shutdown_requested.load()) {
        std::this_thread::sleep_for(std::chrono::seconds(1));
        
        auto now = std::chrono::steady_clock::now();
        auto elapsed = std::chrono::duration_cast<std::chrono::seconds>(now - last_stats_time);
        
        if (elapsed.count() >= stats_interval) {
            g_rtsp_server->print_stats();
            last_stats_time = now;
        }
    }
    
    // 清理
    g_rtsp_server->stop();
    g_rtsp_server.reset();
    
    LOG_I("RTSP server shutdown complete");
    return 0;
}
