# Video Control Service

## 概述

Video Control Service 是一个用于无人机视频控制的服务，提供以下主要功能：

1. **UDP Mavlink 通信**：通过UDP端口14551接收和解析Mavlink消息
2. **SD卡状态监控**：每2秒报告SD卡插入状态、容量、可用空间等信息
3. **拍照功能**：接收Mavlink命令后，从FastDDS视频流中捕获帧并保存为JPEG照片
4. **录像功能**：接收Mavlink命令后，将FastDDS视频流编码为H265格式的MP4文件，自动分段（5分钟/段）

## 编译和安装

```bash
# 编译项目
mkdir build && cd build
cmake ..
make

# 安装
sudo make install
```

## 配置文件

配置文件位于 `config/video_control.json`，主要配置项包括：

### UDP Mavlink 配置
```json
"udp_mavlink": {
  "bind_ip": "0.0.0.0",
  "port": 14551,
  "timeout_ms": 1000
}
```

### SD卡配置
```json
"sdcard": {
  "mount_path": "/media/sdcard",
  "photo_save_path": "/media/sdcard/photos",
  "video_save_path": "/media/sdcard/videos",
  "status_report_interval_sec": 2
}
```

### 录像配置
```json
"video_recording": {
  "codec": "H265",
  "segment_duration_min": 5,
  "width": 1280,
  "height": 720,
  "fps": 30,
  "bitrate": 2000000
}
```

## 使用方法

### 基本启动
```bash
# 使用默认配置启动
./video_control

# 指定配置文件
./video_control --config /path/to/config.json

# 指定UDP端口
./video_control --port 14552

# 启用调试模式
./video_control --debug
```

### 命令行参数

| 参数 | 说明 | 默认值 |
|------|------|--------|
| `-c, --config` | 配置文件路径 | config/video_control.json |
| `-p, --port` | UDP端口 | 14551 |
| `-i, --ip` | UDP绑定IP | 0.0.0.0 |
| `-s, --sdcard` | SD卡挂载路径 | /media/sdcard |
| `--photo-path` | 照片保存路径 | /media/sdcard/photos |
| `--video-path` | 视频保存路径 | /media/sdcard/videos |
| `--topic` | DDS输入主题 | Video_Frames |
| `--codec` | 视频编码格式 | H265 |
| `--segment-duration` | 视频分段时长(分钟) | 5 |
| `--debug` | 启用调试日志 | false |

## Mavlink 命令

服务支持以下Mavlink命令：

### 1. 拍照命令 (MAV_CMD_TAKE_PHOTO = 1)
```
发送字节: [0x01, sequence_id, ...]
功能: 从当前视频流中捕获一帧并保存为JPEG照片
响应: 成功时返回照片路径，失败时返回错误信息
```

### 2. 开始录像 (MAV_CMD_START_RECORDING = 2)
```
发送字节: [0x02, sequence_id, ...]
功能: 开始录制视频，自动创建5分钟分段
响应: 录像开始确认或错误信息
```

### 3. 停止录像 (MAV_CMD_STOP_RECORDING = 3)
```
发送字节: [0x03, sequence_id, ...]
功能: 停止当前录像会话
响应: 录像停止确认，包含录像时长和分段数量
```

### 4. 获取状态 (MAV_CMD_GET_STATUS = 4)
```
发送字节: [0x04, sequence_id, ...]
功能: 获取当前系统状态
响应: SD卡状态、可用空间、录像状态等信息
```

## 文件命名规则

### 照片文件
```
格式: photo_YYYYMMDD_HHMMSS_mmm.jpg
示例: photo_20241224_143052_123.jpg
```

### 视频文件
```
格式: video_[session_id]_YYYYMMDD_HHMMSS_seg[XXX].mp4
示例: video_20241224_143000_20241224_143052_seg001.mp4
```

## 状态监控

服务每2秒自动报告以下状态信息：

- SD卡插入状态
- SD卡挂载状态
- 总容量和可用容量
- 使用百分比
- 当前录像状态
- 录像时长
- 视频分段数量

## 错误处理

### 常见错误及解决方案

1. **SD卡未挂载**
   - 检查SD卡是否正确插入
   - 确认挂载路径配置正确
   - 检查文件系统权限

2. **UDP端口绑定失败**
   - 检查端口是否被其他程序占用
   - 确认防火墙设置
   - 尝试使用其他端口

3. **DDS连接失败**
   - 确认FastDDS服务正在运行
   - 检查DDS主题名称配置
   - 验证网络连接

4. **录像失败**
   - 检查SD卡可用空间
   - 确认GStreamer插件安装完整
   - 检查硬件编码器支持

## 性能优化

### 建议配置

1. **低延迟模式**
   - 减少视频分段时长至2分钟
   - 使用硬件编码器
   - 调整帧队列大小

2. **存储优化模式**
   - 降低视频码率
   - 增加分段时长至10分钟
   - 启用自动清理功能

3. **高质量模式**
   - 提高视频分辨率和码率
   - 使用更高的JPEG质量
   - 增加缓冲区大小

## 日志和调试

### 日志级别
- DEBUG: 详细调试信息
- INFO: 一般信息（默认）
- WARN: 警告信息
- ERROR: 错误信息

### 调试技巧
```bash
# 启用详细日志
./video_control --debug

# 监控系统资源
./video_control --enable-performance-monitoring

# 查看实时状态
kill -USR1 $(pidof video_control)
```

## 集成示例

### Python客户端示例
```python
import socket
import struct

# 创建UDP客户端
sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)

# 发送拍照命令
command = struct.pack('BB', 1, 123)  # 命令类型1，序列号123
sock.sendto(command, ('127.0.0.1', 14551))

# 接收响应
response, addr = sock.recvfrom(1024)
print(f"Response: {response}")

sock.close()
```

## 注意事项

1. **Mavlink库集成**：当前实现使用占位符解析，需要集成实际的Mavlink库
2. **JPEG编码**：照片保存功能需要实现实际的JPEG编码
3. **硬件编码器**：确保系统支持MPP硬件编码器
4. **存储管理**：建议实现自动清理机制防止存储空间耗尽
5. **错误恢复**：服务具备自动重启和错误恢复能力

## 技术支持

如有问题，请检查：
1. 系统日志：`journalctl -u video_control`
2. 配置文件语法
3. 硬件兼容性
4. 依赖库版本
