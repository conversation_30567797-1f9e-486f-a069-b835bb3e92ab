#ifndef VIDEO_CONTROL_H
#define VIDEO_CONTROL_H

#include "common.h"
#include "gstreamer_encoder.h"
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <sys/statvfs.h>
#include <sys/stat.h>
#include <dirent.h>
#include <fstream>
#include <chrono>
#include <iomanip>
#include <sstream>

// Video Stream Configuration for dual camera setup
struct VideoStreamConfig {
    std::string name;           // "visible" or "infrared"
    std::string dds_topic;      // DDS topic name
    int width;                  // Video width
    int height;                 // Video height
    int fps;                    // Frame rate
    int bitrate;                // Bitrate for video recording
    std::string codec;          // "H264" or "H265"
    int jpeg_quality;           // JPEG quality for photos (1-100)

    VideoStreamConfig() : width(1280), height(720), fps(30), bitrate(2000000),
                         codec("H265"), jpeg_quality(95) {}
};

// Video Control Service Configuration
struct VideoControlConfig {
    // UDP Mavlink Configuration
    std::string udp_bind_ip = "0.0.0.0";
    int udp_port = 14551;
    int udp_timeout_ms = 1000;

    // SD Card Configuration
    std::string sdcard_mount_path = "/media/sdcard";
    std::string photo_save_path = "/media/sdcard/photos";
    std::string video_save_path = "/media/sdcard/videos";
    int status_report_interval_sec = 2;

    // Dual Video Stream Configuration
    VideoStreamConfig visible_stream;   // 可见光: 1920x1080, 25fps, 4Mbps
    VideoStreamConfig infrared_stream;  // 红外: 640x512, 30fps, 1Mbps
    int video_segment_duration_min = 5;

    // DDS Configuration
    int domain_id = 0;
    int max_samples = 5;

    // Performance Configuration
    int thread_priority = 80;
    int stats_interval_sec = 10;

    // Logging Configuration
    std::string log_level = "INFO";
    bool enable_debug = false;

    VideoControlConfig() {
        // Initialize visible stream (可见光)
        visible_stream.name = "visible";
        visible_stream.dds_topic = "Video_Frames_Visible";
        visible_stream.width = 1920;
        visible_stream.height = 1080;
        visible_stream.fps = 25;
        visible_stream.bitrate = 4000000; // 4Mbps
        visible_stream.codec = "H265";
        visible_stream.jpeg_quality = 95;

        // Initialize infrared stream (红外)
        infrared_stream.name = "infrared";
        infrared_stream.dds_topic = "Video_Frames_Infrared";
        infrared_stream.width = 640;
        infrared_stream.height = 512;
        infrared_stream.fps = 30;
        infrared_stream.bitrate = 1000000; // 1Mbps
        infrared_stream.codec = "H265";
        infrared_stream.jpeg_quality = 90;
    }
};

// SD Card Status Structure
struct SDCardStatus {
    bool is_inserted = false;
    bool is_mounted = false;
    uint64_t total_capacity_mb = 0;
    uint64_t available_capacity_mb = 0;
    uint64_t used_capacity_mb = 0;
    float usage_percentage = 0.0f;
    std::string filesystem_type;
    std::string mount_point;
    
    // Recording Status
    bool is_recording = false;
    uint64_t recording_duration_sec = 0;
    std::string current_visible_file;
    std::string current_infrared_file;

    uint64_t current_visible_size_mb = 0;
    uint64_t current_infrared_size_mb = 0;
    int total_visible_segments = 0;
    int total_infrared_segments = 0;
};

// Mavlink Message Types (placeholder for future implementation)
enum MavlinkMessageType {
    MAV_CMD_TAKE_PHOTO = 1,
    MAV_CMD_START_RECORDING = 2,
    MAV_CMD_STOP_RECORDING = 3,
    MAV_CMD_GET_STATUS = 4
};

// Mavlink Message Structure (placeholder)
struct MavlinkMessage {
    MavlinkMessageType type;
    uint32_t sequence_id;
    uint64_t timestamp;
    std::vector<uint8_t> payload;
    
    MavlinkMessage() : type(MAV_CMD_GET_STATUS), sequence_id(0), timestamp(0) {}
};

// Photo Capture Result
struct PhotoResult {
    bool success = false;
    std::string error_message;
    std::string photo_path;
    uint64_t photo_size_bytes = 0;
    uint64_t capture_timestamp = 0;
};

// Single Stream Recording Session
struct StreamRecordingSession {
    std::string stream_name;
    std::string current_file_path;
    std::vector<std::string> segment_files;
    uint64_t stream_size_bytes = 0;

    // GStreamer pipeline for encoding
    GstElement* pipeline = nullptr;
    GstElement* appsrc = nullptr;
    GstElement* filesink = nullptr;

    void reset() {
        current_file_path.clear();
        segment_files.clear();
        stream_size_bytes = 0;

        if (pipeline) {
            gst_element_set_state(pipeline, GST_STATE_NULL);
            gst_object_unref(pipeline);
            pipeline = nullptr;
        }
        appsrc = nullptr;
        filesink = nullptr;
    }
};

// Dual Video Recording Session
struct RecordingSession {
    bool is_active = false;
    std::string session_id;
    uint64_t start_timestamp = 0;
    uint64_t current_segment_start = 0;
    int current_segment_index = 0;
    uint64_t total_duration_sec = 0;
    uint64_t total_size_bytes = 0;

    // Dual stream recording sessions
    StreamRecordingSession visible_session;
    StreamRecordingSession infrared_session;

    void reset() {
        is_active = false;
        session_id.clear();
        start_timestamp = 0;
        current_segment_start = 0;
        current_segment_index = 0;
        total_duration_sec = 0;
        total_size_bytes = 0;

        visible_session.reset();
        infrared_session.reset();
    }
};

class VideoControlService {
public:
    VideoControlService();
    ~VideoControlService();
    
    // Service lifecycle
    bool init(const VideoControlConfig& config);
    bool start();
    void stop();
    bool is_running() const { return running_; }
    
    // Signal handling
    void handle_signal(int signal);
    
    // Statistics
    struct Stats {
        uint64_t mavlink_messages_received = 0;
        uint64_t mavlink_messages_processed = 0;
        uint64_t photos_taken = 0;
        uint64_t photos_failed = 0;
        uint64_t recording_sessions = 0;
        uint64_t video_segments_created = 0;
        uint64_t total_recording_duration_sec = 0;
        uint64_t total_data_written_mb = 0;
        float cpu_usage = 0.0f;
        float memory_usage_mb = 0.0f;
    };
    
    void get_stats(Stats& stats) const;
    SDCardStatus get_sdcard_status() const;
    
private:
    // Configuration
    VideoControlConfig config_;
    
    // Service state
    std::atomic<bool> running_{false};
    std::atomic<bool> stop_requested_{false};
    
    // Threading
    std::thread udp_receiver_thread_;
    std::thread status_reporter_thread_;
    std::thread frame_processor_thread_;
    
    // UDP Socket for Mavlink
    int udp_socket_fd_ = -1;
    struct sockaddr_in udp_addr_;
    
    // Dual DDS Video Readers
    std::unique_ptr<DDSVideoReader> visible_dds_reader_;
    std::unique_ptr<DDSVideoReader> infrared_dds_reader_;

    // SD Card and Recording
    mutable std::mutex sdcard_mutex_;
    SDCardStatus sdcard_status_;
    RecordingSession recording_session_;

    // Statistics
    mutable std::mutex stats_mutex_;
    Stats stats_;
    CPUMonitor cpu_monitor_;

    // Frame processing queues for dual streams
    ThreadSafeQueue<Frame> visible_frame_queue_;
    ThreadSafeQueue<Frame> infrared_frame_queue_;

    // GStreamer encoders for photo capture
    std::unique_ptr<GStreamerEncoder> visible_jpeg_encoder_;
    std::unique_ptr<GStreamerEncoder> infrared_jpeg_encoder_;
    
    // Private methods

    // UDP and Mavlink handling
    bool init_udp_socket();
    void udp_receiver_loop();
    bool parse_mavlink_message(const uint8_t* data, size_t length, MavlinkMessage& message);
    void process_mavlink_message(const MavlinkMessage& message);
    void send_mavlink_response(const MavlinkMessage& request, bool success, const std::string& message = "");

    // SD Card management
    void status_reporter_loop();
    bool check_sdcard_status();
    bool ensure_directories_exist();
    uint64_t get_directory_size(const std::string& path);
    bool is_sdcard_mounted(const std::string& mount_path);

    // Photo capture for dual streams
    PhotoResult capture_photo_dual();
    PhotoResult capture_single_photo(const Frame& frame, const std::string& stream_name,
                                    GStreamerEncoder* encoder);
    std::string generate_photo_filename(const std::string& stream_name);
    bool save_frame_as_jpeg(const Frame& frame, const std::string& filepath,
                           GStreamerEncoder* encoder);

    // Video recording for dual streams
    void visible_frame_processor_loop();
    void infrared_frame_processor_loop();
    bool start_recording_session();
    bool stop_recording_session();
    bool create_new_video_segment();
    bool create_stream_video_segment(StreamRecordingSession& session,
                                   const VideoStreamConfig& config);
    bool process_frame_for_recording(const Frame& frame, StreamRecordingSession& session,
                                   const VideoStreamConfig& config);
    std::string generate_video_filename(const std::string& stream_name, int segment_index);
    std::string generate_session_id();

    // GStreamer pipeline management for dual streams
    bool init_stream_gstreamer_pipeline(StreamRecordingSession& session,
                                       const VideoStreamConfig& config);
    void cleanup_stream_gstreamer_pipeline(StreamRecordingSession& session);
    bool push_frame_to_stream_pipeline(const Frame& frame, StreamRecordingSession& session);
    static void on_eos_message(GstBus* bus, GstMessage* message, gpointer user_data);
    static void on_error_message(GstBus* bus, GstMessage* message, gpointer user_data);

    // Utility methods
    std::string format_timestamp(uint64_t timestamp_us);
    std::string format_duration(uint64_t duration_sec);
    std::string format_file_size(uint64_t size_bytes);
    bool create_directory_if_not_exists(const std::string& path);
};

#endif // VIDEO_CONTROL_H
