#ifndef GSTREAMER_ENCODER_H
#define GSTREAMER_ENCODER_H

#include "common.h"
#include <sstream>

// 编码器类型枚举
enum class EncoderType {
    H264,
    H265,
    JPEG
};

// GStreamer编码器接口 - 每个实例只支持一种编码格式
class GStreamerEncoder {
private:
    GstElement* pipeline_ = nullptr;
    GstElement* appsrc_ = nullptr;
    GstElement* appsink_ = nullptr;
    bool gst_initialized_ = false;
    bool pipeline_ready_ = false;

    EncoderType encoder_type_;
    int bitrate_;  // 用于H264/H265
    int quality_;  // 用于JPEG

public:
    // 构造函数 - 指定编码器类型和参数
    explicit GStreamerEncoder(EncoderType type, int param = 2000000);
    ~GStreamerEncoder() { cleanup(); }

    bool init();
    bool encode(const Frame& src, Frame& dst);
    void cleanup();

    // 获取编码器信息
    EncoderType get_encoder_type() const { return encoder_type_; }
    const char* get_encoder_name() const;

private:
    bool create_pipeline();
    bool push_frame_to_pipeline(const Frame& frame);
    bool pull_frame_from_pipeline(Frame& frame);
    std::string create_pipeline_desc();
};

// GStreamerEncoder实现
inline GStreamerEncoder::GStreamerEncoder(EncoderType type, int param)
    : gst_initialized_(false), pipeline_ready_(false), encoder_type_(type) {
    if (type == EncoderType::JPEG) {
        quality_ = param;
        bitrate_ = 0;
    } else {
        bitrate_ = param;
        quality_ = 0;
    }
}

inline bool GStreamerEncoder::init() {
    if (!gst_is_initialized()) {
        gst_init(nullptr, nullptr);
    }

    gst_initialized_ = true;

    // 创建并初始化pipeline
    if (!create_pipeline()) {
        LOG_E("Failed to create %s encoder pipeline", get_encoder_name());
        return false;
    }

    pipeline_ready_ = true;
    LOG_I("GStreamer %s encoder initialized", get_encoder_name());
    return true;
}

inline bool GStreamerEncoder::encode(const Frame& src, Frame& dst) {
    if (!pipeline_ready_) {
        LOG_E("Encoder not initialized");
        return false;
    }

    // 推送数据到pipeline
    if (!push_frame_to_pipeline(src)) {
        LOG_E("Failed to push frame to pipeline");
        return false;
    }

    // 从pipeline拉取编码结果
    if (!pull_frame_from_pipeline(dst)) {
        LOG_E("Failed to pull encoded frame from pipeline");
        return false;
    }

    return true;
}

inline const char* GStreamerEncoder::get_encoder_name() const {
    switch (encoder_type_) {
        case EncoderType::H264: return "H264";
        case EncoderType::H265: return "H265";
        case EncoderType::JPEG: return "JPEG";
        default: return "Unknown";
    }
}

inline bool GStreamerEncoder::create_pipeline() {
    std::string pipeline_desc = create_pipeline_desc();
    if (pipeline_desc.empty()) {
        LOG_E("Failed to create pipeline description");
        return false;
    }

    GError* error = nullptr;
    pipeline_ = gst_parse_launch(pipeline_desc.c_str(), &error);
    if (!pipeline_ || error) {
        LOG_E("Failed to create %s pipeline: %s", get_encoder_name(),
              error ? error->message : "unknown");
        if (error) g_error_free(error);
        return false;
    }

    // 获取appsrc和appsink元素
    appsrc_ = gst_bin_get_by_name(GST_BIN(pipeline_), "source");
    appsink_ = gst_bin_get_by_name(GST_BIN(pipeline_), "sink");

    if (!appsrc_ || !appsink_) {
        LOG_E("Failed to get appsrc or appsink elements");
        cleanup();
        return false;
    }

    // 启动pipeline
    GstStateChangeReturn ret = gst_element_set_state(pipeline_, GST_STATE_PLAYING);
    if (ret == GST_STATE_CHANGE_FAILURE) {
        LOG_E("Failed to start %s pipeline", get_encoder_name());
        cleanup();
        return false;
    }

    return true;
}

inline std::string GStreamerEncoder::create_pipeline_desc() {
    std::ostringstream pipeline;

    pipeline << "appsrc name=source ! queue ! ";

    switch (encoder_type_) {
        case EncoderType::H264:
            pipeline << "mpph264enc bps=" << bitrate_
                     << " bps-min=" << (bitrate_/2)
                     << " bps-max=" << (bitrate_*2)
                     << " profile=baseline gop=15 rc-mode=1 ! ";
            break;

        case EncoderType::H265:
            pipeline << "mpph265enc bps=" << bitrate_
                     << " bps-min=" << (bitrate_/2)
                     << " bps-max=" << (bitrate_*2)
                     << " profile=main gop=15 rc-mode=1 ! ";
            break;

        case EncoderType::JPEG:
            pipeline << "mppjpegenc q-factor=" << quality_ << " ! ";
            break;

        default:
            LOG_E("Unsupported encoder type");
            return "";
    }

    pipeline << "appsink name=sink";
    return pipeline.str();
}

inline void GStreamerEncoder::cleanup() {
    pipeline_ready_ = false;

    if (pipeline_) {
        gst_element_set_state(pipeline_, GST_STATE_NULL);
        gst_object_unref(pipeline_);
        pipeline_ = nullptr;
    }

    if (appsrc_) {
        gst_object_unref(appsrc_);
        appsrc_ = nullptr;
    }

    if (appsink_) {
        gst_object_unref(appsink_);
        appsink_ = nullptr;
    }
}

inline bool GStreamerEncoder::push_frame_to_pipeline(const Frame& frame) {
    if (!appsrc_ || frame.data.empty()) {
        return false;
    }

    GstBuffer* buffer = gst_buffer_new_allocate(nullptr, frame.data.size(), nullptr);
    if (!buffer) {
        LOG_E("Failed to allocate GstBuffer");
        return false;
    }

    GstMapInfo map;
    if (!gst_buffer_map(buffer, &map, GST_MAP_WRITE)) {
        LOG_E("Failed to map GstBuffer");
        gst_buffer_unref(buffer);
        return false;
    }

    memcpy(map.data, frame.data.data(), frame.data.size());
    gst_buffer_unmap(buffer, &map);

    GstFlowReturn ret = gst_app_src_push_buffer(GST_APP_SRC(appsrc_), buffer);
    if (ret != GST_FLOW_OK) {
        LOG_E("Failed to push buffer to appsrc: %d", ret);
        return false;
    }

    return true;
}

inline bool GStreamerEncoder::pull_frame_from_pipeline(Frame& frame) {
    if (!appsink_) {
        return false;
    }

    GstSample* sample = gst_app_sink_pull_sample(GST_APP_SINK(appsink_));
    if (!sample) {
        LOG_E("Failed to pull sample from appsink");
        return false;
    }

    GstBuffer* buffer = gst_sample_get_buffer(sample);
    if (!buffer) {
        LOG_E("Failed to get buffer from sample");
        gst_sample_unref(sample);
        return false;
    }

    GstMapInfo map;
    if (!gst_buffer_map(buffer, &map, GST_MAP_READ)) {
        LOG_E("Failed to map buffer");
        gst_sample_unref(sample);
        return false;
    }

    // 复制数据到Frame
    frame.data.resize(map.size);
    memcpy(frame.data.data(), map.data, map.size);

    gst_buffer_unmap(buffer, &map);
    gst_sample_unref(sample);

    return true;
}

#endif // GSTREAMER_ENCODER_H
